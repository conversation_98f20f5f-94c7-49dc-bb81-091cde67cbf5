#!/bin/bash

# 最终验证脚本 - 确认所有修复都已生效
echo "🎯 最终验证：CDN优先级和压缩文件修复"
echo "========================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 测试计数
total_tests=0
passed_tests=0

test_result() {
    ((total_tests++))
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
        ((passed_tests++))
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

echo -e "${BLUE}📊 1. 验证文件大小对比${NC}"
echo "================================"

echo "📁 本地文件大小对比："
echo "原始文件 vs 压缩文件 (together-days为例):"

original_size=$(ls -lh background/together-days/together-days.mp4 | awk '{print $5}')
compressed_size=$(ls -lh background/cloudinary-ready/together-days.mp4 | awk '{print $5}')

echo "  原始文件: $original_size"
echo "  压缩文件: $compressed_size"

# 检查压缩效果
original_bytes=$(ls -l background/together-days/together-days.mp4 | awk '{print $5}')
compressed_bytes=$(ls -l background/cloudinary-ready/together-days.mp4 | awk '{print $5}')

if [ $compressed_bytes -lt $original_bytes ]; then
    test_result 0 "together-days文件已正确压缩 ($original_size → $compressed_size)"
else
    test_result 1 "together-days文件压缩异常"
fi

echo -e "\n${BLUE}📊 2. 验证Cloudinary连接${NC}"
echo "================================"

# 测试所有Cloudinary视频
videos=("home" "meetings" "anniversary" "memorial" "together-days")
cloudinary_success=0

for video in "${videos[@]}"; do
    echo -n "测试 $video... "
    status=$(curl -s -o /dev/null -w "%{http_code}" "https://res.cloudinary.com/dcglebc2w/video/upload/love-website/$video.mp4")
    
    if [ "$status" = "200" ]; then
        echo -e "${GREEN}✅ $status${NC}"
        ((cloudinary_success++))
    else
        echo -e "${RED}❌ $status${NC}"
    fi
done

test_result $((5 - cloudinary_success)) "Cloudinary视频可访问性 ($cloudinary_success/5)"

echo -e "\n${BLUE}📊 3. 验证JavaScript文件修复${NC}"
echo "================================"

# 检查hybrid-cdn-manager.js中的映射
echo "检查hybrid-cdn-manager.js映射..."
if grep -q "'INDEX':" hybrid-cdn-manager.js && grep -q "'TOGETHER_DAYS':" hybrid-cdn-manager.js; then
    test_result 0 "HybridCDNManager映射已修复（包含大写键）"
else
    test_result 1 "HybridCDNManager映射未修复"
fi

# 检查cloudinary-load-balancer.js中的配置
echo "检查cloudinary-load-balancer.js配置..."
if grep -q "'INDEX':" cloudinary-load-balancer.js && grep -q "'TOGETHER_DAYS':" cloudinary-load-balancer.js; then
    test_result 0 "CloudinaryLoadBalancer配置已修复（包含大写键）"
else
    test_result 1 "CloudinaryLoadBalancer配置未修复"
fi

# 检查优先级设置
echo "检查CDN优先级设置..."
if grep -q "priority: 1" hybrid-cdn-manager.js | head -1; then
    test_result 0 "Cloudinary优先级设置正确"
else
    test_result 1 "Cloudinary优先级设置异常"
fi

echo -e "\n${BLUE}📊 4. 验证本地备用文件${NC}"
echo "================================"

# 检查所有压缩文件是否存在
compressed_files=("home.mp4" "meetings.mp4" "anniversary.mp4" "memorial.mp4" "together-days.mp4")
local_files_ok=0

for file in "${compressed_files[@]}"; do
    if [ -f "background/cloudinary-ready/$file" ]; then
        size=$(ls -lh "background/cloudinary-ready/$file" | awk '{print $5}')
        echo "  ✅ $file: $size"
        ((local_files_ok++))
    else
        echo "  ❌ $file: 不存在"
    fi
done

test_result $((5 - local_files_ok)) "本地压缩文件完整性 ($local_files_ok/5)"

echo -e "\n${BLUE}📊 5. 运行映射测试${NC}"
echo "================================"

# 运行之前创建的映射测试
echo "运行映射测试脚本..."
if node simple-mapping-test.js > /tmp/mapping_test.log 2>&1; then
    success_count=$(grep -c "映射正确\|配置正确" /tmp/mapping_test.log)
    if [ $success_count -eq 10 ]; then
        test_result 0 "所有映射测试通过 (10/10)"
    else
        test_result 1 "映射测试部分失败 ($success_count/10)"
    fi
else
    test_result 1 "映射测试脚本执行失败"
fi

echo -e "\n${BLUE}📊 6. 验证CDN优先级逻辑${NC}"
echo "================================"

# 运行CDN优先级测试
echo "运行CDN优先级测试..."
if node test-cdn-priority.js > /tmp/priority_test.log 2>&1; then
    cloudinary_priority_count=$(grep -c "正确优先选择Cloudinary" /tmp/priority_test.log)
    if [ $cloudinary_priority_count -eq 5 ]; then
        test_result 0 "CDN优先级逻辑正确 (5/5页面优先选择Cloudinary)"
    else
        test_result 1 "CDN优先级逻辑异常 ($cloudinary_priority_count/5)"
    fi
else
    test_result 1 "CDN优先级测试执行失败"
fi

echo -e "\n========================================"
echo -e "${BLUE}📊 最终测试报告${NC}"
echo "========================================"
echo "总测试项: $total_tests"
echo -e "通过测试: ${GREEN}$passed_tests${NC}"
echo -e "失败测试: ${RED}$((total_tests - passed_tests))${NC}"

success_rate=$((passed_tests * 100 / total_tests))
echo "成功率: $success_rate%"

if [ $passed_tests -eq $total_tests ]; then
    echo -e "\n${GREEN}🎉 所有修复验证通过！${NC}"
    echo
    echo "✅ 修复总结:"
    echo "  • Cloudinary CDN优先级已修复"
    echo "  • 所有页面映射已修复（支持大写键）"
    echo "  • 本地备选文件使用压缩版本"
    echo "  • together-days从146MB压缩到24MB"
    echo "  • 故障转移逻辑已优化"
    echo
    echo "🌐 现在访问网站应该："
    echo "  1. 所有页面优先从Cloudinary加载视频"
    echo "  2. 加载速度显著提升"
    echo "  3. 故障时自动使用压缩的本地文件"
    echo
    echo "🔗 测试链接："
    echo "  • 主页: https://love.yuh.cool/"
    echo "  • 海底页面: https://love.yuh.cool/together-days"
    echo "  • 测试页面: https://love.yuh.cool/test-fix.html"
    
else
    echo -e "\n${YELLOW}⚠️  发现 $((total_tests - passed_tests)) 个问题需要进一步处理${NC}"
fi

echo -e "\n🔧 修复完成！可以清理测试文件："
echo "rm test-*.js test-*.html verify-fix.sh final-verification.sh"
