<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主页修复验证 - Love Website</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #fff;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .test-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .test-button.primary {
            background: linear-gradient(45deg, #2196F3, #1976D2);
        }
        
        .info-display {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-good { background-color: #4CAF50; }
        .status-warning { background-color: #FF9800; }
        .status-error { background-color: #F44336; }
        
        .video-preview {
            width: 100%;
            max-width: 600px;
            height: 300px;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        .link-button {
            display: inline-block;
            background: linear-gradient(45deg, #FF9800, #F57C00);
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 6px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .link-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏠 主页修复验证工具</h1>
        <p style="text-align: center; opacity: 0.8;">验证主页视频修复是否生效</p>
        
        <div class="test-section">
            <h3>🔗 快速访问</h3>
            <div style="text-align: center;">
                <a href="https://love.yuh.cool/" class="link-button" target="_blank">访问主页</a>
                <a href="https://love.yuh.cool/test/test-emergency-fix.html" class="link-button" target="_blank">紧急修复测试</a>
                <a href="https://love.yuh.cool/test/test-video-resolution-debug.html" class="link-button" target="_blank">分辨率调试</a>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 主页URL测试</h3>
            <div class="button-group">
                <button class="test-button" onclick="testHomepageUrl()">测试主页URL生成</button>
                <button class="test-button primary" onclick="loadTestVideo()">加载测试视频</button>
            </div>
            <div id="url-test-results" class="info-display">点击按钮开始测试...</div>
        </div>
        
        <div class="test-section">
            <h3>🎥 视频预览</h3>
            <video id="test-video" class="video-preview" controls muted></video>
            <div id="video-info" class="info-display">未加载视频</div>
        </div>
        
        <div class="test-section">
            <h3>🔍 设备检测信息</h3>
            <div id="device-info" class="info-display">检测中...</div>
            <button class="test-button" onclick="updateDeviceInfo()">刷新设备信息</button>
        </div>
        
        <div class="test-section">
            <h3>📋 修复指南</h3>
            <div class="info-display">
如果主页仍然显示错误，请按以下步骤操作：

1. 🔄 刷新主页 (Ctrl+F5 或 Cmd+Shift+R)
2. 🧪 访问测试页面验证修复脚本是否工作
3. 🔧 在主页控制台运行: emergencyVideoFix()
4. 📱 检查设备检测是否正确
5. 🌐 确保使用域名访问而非端口号

预期结果：
- 桌面设备: 2560x1440 (2K分辨率)
- 移动设备: 1920x1080 (高清分辨率)
- 无400/404错误
- VideoManager正确初始化
            </div>
        </div>
    </div>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🏠 主页修复验证工具已加载');
            updateDeviceInfo();
        });
        
        function updateDeviceInfo() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            const userAgent = navigator.userAgent;
            
            const isMobileUA = /Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
            const isTabletUA = /iPad|Android.*Tablet|Windows.*Touch/i.test(userAgent) && !/Mobile/i.test(userAgent);
            
            // 使用与主页相同的检测逻辑
            const isMobile = width <= 480 || (width <= 768 && isMobileUA);
            const isTablet = (width > 480 && width <= 1024 && isTabletUA) || 
                            (width > 768 && width <= 1024 && !isMobile);
            
            let deviceType = 'Desktop';
            let expectedResolution = '2560x1440 (2K)';
            let statusClass = 'status-good';
            
            if (isMobile) {
                deviceType = 'Mobile';
                expectedResolution = '1920x1080 (1080p)';
                statusClass = 'status-warning';
            } else if (isTablet) {
                deviceType = 'Tablet';
                expectedResolution = '2560x1440 (2K)';
                statusClass = 'status-warning';
            }
            
            const info = `设备类型: ${deviceType}
屏幕尺寸: ${width}x${height}
预期视频分辨率: ${expectedResolution}
User Agent检测: ${isMobileUA ? 'Mobile' : isTabletUA ? 'Tablet' : 'Desktop'}
设备像素比: ${window.devicePixelRatio}

检测逻辑:
- 移动设备: 宽度≤480px 或 (宽度≤768px 且 UA为移动设备)
- 平板设备: 481-1024px 且 UA为平板设备
- 桌面设备: 其他情况

当前判断依据:
- 宽度≤480: ${width <= 480}
- 宽度≤768: ${width <= 768}
- UA为移动: ${isMobileUA}
- UA为平板: ${isTabletUA}`;
            
            document.getElementById('device-info').textContent = info;
        }
        
        function testHomepageUrl() {
            const resultsElement = document.getElementById('url-test-results');
            let results = '🧪 主页URL生成测试:\n\n';
            
            // 模拟主页的URL生成逻辑
            const videoConfigs = {
                'INDEX': {
                    cloudName: 'dcglebc2w',
                    publicId: 'love-website/home'
                }
            };
            
            const config = videoConfigs['INDEX'];
            const screenWidth = window.innerWidth;
            const userAgent = navigator.userAgent;
            const isMobileUA = /Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
            const isMobile = screenWidth <= 480 || (screenWidth <= 768 && isMobileUA);
            
            let transforms;
            if (isMobile) {
                transforms = 'q_95,f_auto,w_1920,h_1080,c_limit,fl_immutable_cache';
                results += '📱 检测为移动设备\n';
                results += '预期分辨率: 1920x1080\n';
            } else {
                transforms = 'q_100,f_auto,w_2560,h_1440,c_limit,fl_immutable_cache';
                results += '🖥️ 检测为桌面设备\n';
                results += '预期分辨率: 2560x1440\n';
            }
            
            const url = `https://res.cloudinary.com/${config.cloudName}/video/upload/${transforms}/${config.publicId}.mp4`;
            
            results += `\n生成的URL:\n${url}\n\n`;
            
            // 分析URL参数
            const resMatch = url.match(/w_(\d+),h_(\d+)/);
            if (resMatch) {
                results += `✅ 检测到分辨率参数: ${resMatch[1]}x${resMatch[2]}\n`;
            } else {
                results += '❌ 未检测到分辨率参数\n';
            }
            
            const qualityMatch = url.match(/q_(\d+)/);
            if (qualityMatch) {
                results += `✅ 检测到质量参数: ${qualityMatch[1]}\n`;
            }
            
            results += '\n🔍 URL格式检查:\n';
            results += `- 包含cloudName: ${url.includes(config.cloudName) ? '✅' : '❌'}\n`;
            results += `- 包含publicId: ${url.includes(config.publicId) ? '✅' : '❌'}\n`;
            results += `- 包含分辨率: ${url.includes('w_') && url.includes('h_') ? '✅' : '❌'}\n`;
            results += `- 包含质量设置: ${url.includes('q_') ? '✅' : '❌'}\n`;
            
            resultsElement.textContent = results;
        }
        
        function loadTestVideo() {
            const video = document.getElementById('test-video');
            const info = document.getElementById('video-info');
            
            // 生成测试URL
            const screenWidth = window.innerWidth;
            const userAgent = navigator.userAgent;
            const isMobileUA = /Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
            const isMobile = screenWidth <= 480 || (screenWidth <= 768 && isMobileUA);
            
            let transforms;
            if (isMobile) {
                transforms = 'q_95,f_auto,w_1920,h_1080,c_limit,fl_immutable_cache';
            } else {
                transforms = 'q_100,f_auto,w_2560,h_1440,c_limit,fl_immutable_cache';
            }
            
            const url = `https://res.cloudinary.com/dcglebc2w/video/upload/${transforms}/love-website/home.mp4`;
            
            info.textContent = `加载中...\nURL: ${url}`;
            
            video.addEventListener('loadedmetadata', function() {
                const resolution = `${video.videoWidth}x${video.videoHeight}`;
                info.textContent = `✅ 加载成功\n实际分辨率: ${resolution}\nURL: ${url}`;
                console.log(`✅ 测试视频加载成功，分辨率: ${resolution}`);
            }, { once: true });
            
            video.addEventListener('error', function(e) {
                info.textContent = `❌ 加载失败\n错误: ${e.message || '未知错误'}\nURL: ${url}`;
                console.error('❌ 测试视频加载失败:', e);
            }, { once: true });
            
            video.src = url;
            video.load();
        }
        
        // 监听窗口大小变化
        window.addEventListener('resize', function() {
            setTimeout(updateDeviceInfo, 100);
        });
    </script>
</body>
</html>
