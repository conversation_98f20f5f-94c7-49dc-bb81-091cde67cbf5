#!/usr/bin/env node

/**
 * Service Worker缓存性能演示脚本
 * 模拟多次请求来展示缓存效果
 */

const https = require('https');
const http = require('http');

// 测试配置
const BASE_URL = 'http://localhost:1314';
const TEST_ITERATIONS = 3;

// 测试资源列表
const TEST_RESOURCES = [
    {
        name: '主页',
        url: '/',
        type: 'page'
    },
    {
        name: '配置文件',
        url: '/config.js',
        type: 'static'
    },
    {
        name: 'VideoManager',
        url: '/simple-video-manager.js',
        type: 'static'
    },
    {
        name: 'API接口',
        url: '/api/messages',
        type: 'api'
    }
];

// 发起HTTP请求
function makeRequest(url) {
    return new Promise((resolve, reject) => {
        const startTime = Date.now();

        const options = {
            ...require('url').parse(url),
            family: 4 // 强制使用IPv4
        };

        const request = http.get(options, (response) => {
            let data = '';
            
            response.on('data', (chunk) => {
                data += chunk;
            });
            
            response.on('end', () => {
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                resolve({
                    statusCode: response.statusCode,
                    responseTime: responseTime,
                    dataSize: data.length,
                    headers: response.headers
                });
            });
        });
        
        request.on('error', (error) => {
            reject(error);
        });
        
        request.setTimeout(10000, () => {
            request.destroy();
            reject(new Error('Request timeout'));
        });
    });
}

// 运行性能测试
async function runPerformanceTest() {
    console.log('🚀 Service Worker缓存性能演示');
    console.log('=' .repeat(50));
    console.log();
    
    for (const resource of TEST_RESOURCES) {
        console.log(`📊 测试资源: ${resource.name} (${resource.type})`);
        console.log(`🔗 URL: ${resource.url}`);
        
        const times = [];
        const sizes = [];
        
        for (let i = 1; i <= TEST_ITERATIONS; i++) {
            try {
                const result = await makeRequest(BASE_URL + resource.url);
                
                if (result.statusCode === 200) {
                    times.push(result.responseTime);
                    sizes.push(result.dataSize);
                    
                    console.log(`  第${i}次请求: ${result.responseTime}ms (${result.dataSize} bytes)`);
                    
                    // 检查缓存相关头部
                    if (result.headers['cache-control']) {
                        console.log(`    Cache-Control: ${result.headers['cache-control']}`);
                    }
                    
                } else {
                    console.log(`  第${i}次请求失败: HTTP ${result.statusCode}`);
                }
                
                // 请求间隔
                if (i < TEST_ITERATIONS) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                }
                
            } catch (error) {
                console.log(`  第${i}次请求异常: ${error.message}`);
            }
        }
        
        // 计算统计信息
        if (times.length > 0) {
            const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
            const minTime = Math.min(...times);
            const maxTime = Math.max(...times);
            const avgSize = sizes.reduce((a, b) => a + b, 0) / sizes.length;
            
            console.log(`  📈 统计结果:`);
            console.log(`    平均响应时间: ${avgTime.toFixed(2)}ms`);
            console.log(`    最快响应时间: ${minTime}ms`);
            console.log(`    最慢响应时间: ${maxTime}ms`);
            console.log(`    平均数据大小: ${avgSize.toFixed(0)} bytes`);
            
            // 分析缓存效果
            if (times.length >= 2) {
                const firstTime = times[0];
                const subsequentTimes = times.slice(1);
                const avgSubsequent = subsequentTimes.reduce((a, b) => a + b, 0) / subsequentTimes.length;
                
                if (avgSubsequent < firstTime * 0.8) {
                    console.log(`  ✅ 缓存效果明显: 后续请求平均快 ${((firstTime - avgSubsequent) / firstTime * 100).toFixed(1)}%`);
                } else if (avgSubsequent < firstTime) {
                    console.log(`  🔄 轻微缓存效果: 后续请求平均快 ${((firstTime - avgSubsequent) / firstTime * 100).toFixed(1)}%`);
                } else {
                    console.log(`  ⚠️  未检测到明显缓存效果`);
                }
            }
        }
        
        console.log();
    }
    
    console.log('📋 测试总结:');
    console.log('1. Service Worker缓存主要在浏览器环境中生效');
    console.log('2. 命令行测试主要验证服务器响应性能');
    console.log('3. 真实缓存效果需要在浏览器中测试');
    console.log('4. 建议访问 http://localhost:1314/test-service-worker.html 进行完整测试');
    console.log();
    
    console.log('🎯 Service Worker缓存策略:');
    console.log('- 视频文件: Cache First (优先使用缓存)');
    console.log('- 静态资源: Stale While Revalidate (缓存优先，后台更新)');
    console.log('- API请求: Network First (网络优先，缓存备用)');
    console.log('- 缓存大小: 视频200MB，静态资源50MB');
}

// 检查服务器状态
async function checkServerStatus() {
    try {
        const result = await makeRequest(BASE_URL + '/');
        if (result.statusCode === 200) {
            console.log('✅ 服务器运行正常');
            return true;
        } else {
            console.log(`❌ 服务器响应异常: HTTP ${result.statusCode}`);
            return false;
        }
    } catch (error) {
        console.log(`❌ 无法连接到服务器: ${error.message}`);
        console.log('请确保服务器在 http://localhost:1314 运行');
        return false;
    }
}

// 主函数
async function main() {
    console.log('🔍 检查服务器状态...');
    
    const serverOk = await checkServerStatus();
    if (!serverOk) {
        process.exit(1);
    }
    
    console.log();
    await runPerformanceTest();
}

// 运行演示
main().catch(error => {
    console.error('❌ 演示失败:', error.message);
    process.exit(1);
});
