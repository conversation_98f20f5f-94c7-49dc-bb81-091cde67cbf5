/**
 * Simple Video Manager - 简单直接的视频管理器
 * 直接加载视频，无复杂优化策略
 */

class SimpleVideoManager {
    constructor() {
        console.log('🚀 Simple Video Manager initialized');
        this.currentVideo = null;
        this.loadingPromises = new Map();
    }

    /**
     * 直接加载视频
     */
    async loadVideo(pageKey, videoConfig) {
        console.log(`🎬 Loading video for ${pageKey}:`, videoConfig.name);

        try {
            // 创建视频元素
            const video = document.createElement('video');
            video.autoplay = true;
            video.muted = true;
            video.loop = true;
            video.playsInline = true;
            video.preload = 'auto';

            // 设置视频源 - 直接使用原始文件
            const source = document.createElement('source');
            source.src = videoConfig.url;
            source.type = 'video/mp4';
            video.appendChild(source);

            // 等待视频加载
            await this.waitForVideoLoad(video);

            this.currentVideo = video;
            console.log(`✅ Video loaded successfully for ${pageKey}`);
            return video;

        } catch (error) {
            console.error(`❌ Failed to load video for ${pageKey}:`, error);
            throw error;
        }
    }

    /**
     * 等待视频加载完成
     */
    waitForVideoLoad(video) {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Video load timeout'));
            }, 30000); // 30秒超时

            video.addEventListener('loadeddata', () => {
                clearTimeout(timeout);
                resolve(video);
            });

            video.addEventListener('error', (e) => {
                clearTimeout(timeout);
                reject(new Error(`Video load error: ${e.message}`));
            });
        });
    }
    detectDeviceCapability() {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        
        const capability = {
            memory: navigator.deviceMemory || 4,
            cores: navigator.hardwareConcurrency || 4,
            gpu: gl ? gl.getParameter(gl.RENDERER) : 'unknown',
            screen: {
                width: screen.width,
                height: screen.height,
                pixelRatio: window.devicePixelRatio || 1
            }
        };

        // 判断设备等级
        let level = 'medium';
        if (capability.memory >= 8 && capability.cores >= 8) {
            level = 'high';
        } else if (capability.memory <= 2 || capability.cores <= 2) {
            level = 'low';
        }

        return { ...capability, level };
    }

    /**
     * 检测压缩格式支持
     */
    detectCompressionSupport() {
        const video = document.createElement('video');
        return {
            webm: video.canPlayType('video/webm; codecs="vp9"') !== '',
            h264: video.canPlayType('video/mp4; codecs="avc1.42E01E"') !== '',
            h265: video.canPlayType('video/mp4; codecs="hev1.1.6.L93.B0"') !== ''
        };
    }

    /**
     * 获取最优加载策略 (保持2K质量)
     */
    getOptimalQualitySettings() {
        const { speed } = this.networkType;
        const { level } = this.deviceCapability;

        // 加载策略矩阵 - 保持原始质量，优化加载方式
        const loadingStrategy = {
            fast: {
                format: 'h265',      // 优先H.265
                preload: 'auto',
                progressive: false,   // 直接加载最佳版本
                timeout: 15000
            },
            medium: {
                format: 'h264',      // 使用H.264优化版
                preload: 'metadata',
                progressive: true,    // 启用渐进式加载
                timeout: 25000
            },
            slow: {
                format: 'webm',      // WebM压缩率更好
                preload: 'none',
                progressive: true,
                timeout: 45000
            }
        };

        const strategy = loadingStrategy[speed] || loadingStrategy.medium;

        return {
            format: strategy.format,
            preload: strategy.preload,
            progressive: strategy.progressive,
            timeout: strategy.timeout,
            keepOriginalQuality: true // 标记保持原始质量
        };
    }

    /**
     * 获取最佳视频URL (保持2K质量)
     */
    getBestVideoUrl(videoConfig) {
        const { format } = this.qualitySettings;
        const basePath = videoConfig.file.replace(/\.[^/.]+$/, "");
        const dir = videoConfig.file.includes('/') ?
            videoConfig.file.substring(0, videoConfig.file.lastIndexOf('/')) : '';

        // 构建优化版本路径 - 修复路径构建逻辑
        let optimizedBase;
        if (dir) {
            optimizedBase = `/background/optimized/${dir}/${basePath.split('/').pop()}`;
        } else {
            optimizedBase = `/background/optimized/${basePath}`;
        }

        // 根据网络条件选择最佳格式
        let primaryUrl, fallbackUrl;

        if (format === 'h265' && this.compressionSupport.h265) {
            primaryUrl = `${optimizedBase}_h265_optimized.mp4`;
            fallbackUrl = `${optimizedBase}_h264_optimized.mp4`;
        } else if (format === 'webm' && this.compressionSupport.webm) {
            primaryUrl = `${optimizedBase}_vp9_optimized.webm`;
            fallbackUrl = `${optimizedBase}_h264_optimized.mp4`;
        } else {
            primaryUrl = `${optimizedBase}_h264_optimized.mp4`;
            fallbackUrl = `${optimizedBase}_h264_optimized.mp4`;
        }

        // 如果优化版本不存在，直接使用原始文件
        return {
            primary: primaryUrl,
            fallback: fallbackUrl,
            original: videoConfig.url,
            format: format
        };
    }

    /**
     * 渐进式加载视频
     */
    async loadVideoProgressively(pageKey, videoConfig) {
        const urls = this.getBestVideoUrl(videoConfig);

        console.log(`🎬 开始渐进式加载: ${pageKey}`);
        console.log('📋 URL策略:', urls);

        // 尝试加载视频的优先级顺序：primary -> fallback -> original
        let video;
        const loadOrder = [
            { url: urls.primary, name: '高质量版本' },
            { url: urls.fallback, name: '备用版本' },
            { url: urls.original, name: '原始版本' }
        ];

        for (const { url, name } of loadOrder) {
            try {
                console.log(`🔄 尝试加载${name}: ${url}`);
                video = await this.loadSingleVideo(url, 'normal');
                console.log(`✅ ${name}加载成功: ${pageKey}`);

                // 立即显示视频
                this.displayVideo(video, pageKey);
                return video;

            } catch (error) {
                console.log(`⚠️ ${name}加载失败: ${error.message}`);
                // 继续尝试下一个版本
            }
        }

        // 如果所有版本都失败了，抛出错误
        throw new Error(`所有视频版本都加载失败: ${pageKey}`);
    }

    /**
     * 后台加载高质量版本
     */
    async loadHighQualityInBackground(highQualityUrl, pageKey, currentVideo) {
        try {
            console.log(`🔄 后台加载高质量版本: ${pageKey}`);
            const highQualityVideo = await this.loadSingleVideo(highQualityUrl, 'background');
            
            // 平滑切换到高质量版本
            await this.smoothTransition(currentVideo, highQualityVideo);
            console.log(`⬆️ 已升级到高质量版本: ${pageKey}`);
            
        } catch (error) {
            console.log(`ℹ️ 高质量版本加载失败，保持当前质量: ${pageKey}`);
        }
    }

    /**
     * 加载单个视频
     */
    loadSingleVideo(url, priority = 'normal') {
        return new Promise((resolve, reject) => {
            const video = document.createElement('video');
            video.muted = true;
            video.loop = true;
            video.playsInline = true;
            video.preload = priority === 'fast' ? 'metadata' : 'auto';
            
            // 设置样式
            video.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: cover;
                z-index: -1;
                opacity: 0;
                transition: opacity 0.8s ease-in-out;
                transform: translateZ(0);
                backface-visibility: hidden;
            `;

            const timeout = priority === 'fast' ? 10000 : 
                           priority === 'background' ? 30000 : 20000;

            const timer = setTimeout(() => {
                reject(new Error(`视频加载超时: ${url}`));
            }, timeout);

            video.addEventListener('canplaythrough', () => {
                clearTimeout(timer);
                resolve(video);
            }, { once: true });

            video.addEventListener('error', (error) => {
                clearTimeout(timer);
                reject(error);
            }, { once: true });

            video.src = url;
            video.load();
        });
    }

    /**
     * 显示视频
     */
    displayVideo(video, pageKey) {
        // 隐藏其他视频
        document.querySelectorAll('video').forEach(v => {
            if (v !== video) {
                v.style.opacity = '0';
            }
        });

        // 确保视频在DOM中
        if (!video.parentNode) {
            document.body.appendChild(video);
        }

        // 淡入显示
        setTimeout(() => {
            video.style.opacity = '1';
            video.play().catch(console.log);
        }, 100);

        // 缓存视频
        this.cache.set(pageKey, video);
    }

    /**
     * 平滑过渡到高质量版本
     */
    async smoothTransition(oldVideo, newVideo) {
        return new Promise((resolve) => {
            // 准备新视频
            newVideo.currentTime = oldVideo.currentTime;
            newVideo.style.opacity = '0';
            
            if (!newVideo.parentNode) {
                document.body.appendChild(newVideo);
            }

            // 开始播放新视频
            newVideo.play().then(() => {
                // 淡入新视频，淡出旧视频
                newVideo.style.opacity = '1';
                oldVideo.style.opacity = '0';

                // 移除旧视频
                setTimeout(() => {
                    if (oldVideo.parentNode) {
                        oldVideo.parentNode.removeChild(oldVideo);
                    }
                    resolve();
                }, 800);
            });
        });
    }

    /**
     * 为页面加载视频（主入口）
     */
    async loadVideoForPage(pageKey) {
        const config = window.CONFIG?.VIDEOS?.PAGES?.[pageKey];
        if (!config) {
            console.log(`❌ 页面配置不存在: ${pageKey}`);
            return;
        }

        try {
            const video = await this.loadVideoProgressively(pageKey, config);
            console.log(`🎉 视频加载完成: ${pageKey}`);
            return video;
        } catch (error) {
            console.error(`❌ 视频加载失败: ${pageKey}`, error);
            this.applyFallbackBackground(config.theme);
        }
    }

    /**
     * 兼容方法：loadVideo - 为了兼容旧的VideoManager接口
     */
    async loadVideo(pageKey, videoConfig) {
        try {
            const video = await this.loadVideoProgressively(pageKey, videoConfig);
            console.log(`🎉 视频加载完成: ${pageKey}`);
            return video;
        } catch (error) {
            console.error(`❌ 视频加载失败: ${pageKey}`, error);
            this.applyFallbackBackground(videoConfig.theme);
        }
    }

    /**
     * 兼容方法：preloadVideo - 为了兼容旧的VideoManager接口
     */
    async preloadVideo(pageKey, videoConfig, priority = 1) {
        // 检查是否已缓存
        if (this.cache.has(pageKey)) {
            console.log(`📋 视频已缓存: ${pageKey}`);
            return;
        }

        console.log(`🔄 预加载视频: ${pageKey} (优先级: ${priority})`);

        try {
            await this.loadVideo(pageKey, videoConfig);
        } catch (error) {
            console.log(`⚠️ 预加载失败: ${pageKey}`, error);
        }
    }

    /**
     * 兼容方法：pauseAllVideos - 为了兼容旧的VideoManager接口
     */
    pauseAllVideos() {
        document.querySelectorAll('video').forEach(video => {
            video.pause();
        });
    }

    /**
     * 兼容方法：resumeCurrentVideo - 为了兼容旧的VideoManager接口
     */
    resumeCurrentVideo() {
        const currentPageKey = this.getCurrentPageKey();
        const video = this.cache.get(currentPageKey);
        if (video && video.parentNode) {
            video.play().catch(console.log);
        }
    }

    /**
     * 兼容方法：getCacheStatus - 为了兼容旧的VideoManager接口
     */
    getCacheStatus() {
        const status = {
            cacheSize: this.cache.size,
            cachedPages: Array.from(this.cache.keys()),
            loadingQueue: Array.from(this.loadingQueue.keys())
        };
        return status;
    }

    /**
     * 应用备用背景
     */
    applyFallbackBackground(theme) {
        const fallbackConfig = window.CONFIG?.VIDEOS?.FALLBACK_THEMES?.[theme];
        if (fallbackConfig) {
            document.body.style.background = fallbackConfig.gradient;
            document.body.style.animation = fallbackConfig.animation;
        }
    }

    /**
     * 获取当前页面键
     */
    getCurrentPageKey() {
        const path = window.location.pathname;
        const filename = path.split('/').pop() || 'index.html';
        return window.CONFIG?.VIDEOS?.PAGE_MAPPING?.[filename] || 'INDEX';
    }
}

// 全局导出
window.EnhancedVideoManager = EnhancedVideoManager;

// 兼容性导出 - 为了兼容使用 window.VideoManager 的代码
window.VideoManager = new EnhancedVideoManager();
