<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频分辨率修复测试 - Love Website</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #fff;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .test-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .test-button.secondary {
            background: linear-gradient(45deg, #FF9800, #F57C00);
        }
        
        .test-button.danger {
            background: linear-gradient(45deg, #F44336, #D32F2F);
        }
        
        .video-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .video-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .video-card h4 {
            margin-top: 0;
            color: #FFD700;
        }
        
        .video-preview {
            width: 100%;
            height: 200px;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        .info-display {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-good { background-color: #4CAF50; }
        .status-warning { background-color: #FF9800; }
        .status-error { background-color: #F44336; }
        
        .log-output {
            background: rgba(0, 0, 0, 0.5);
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 视频分辨率修复测试</h1>
        <p style="text-align: center; opacity: 0.8;">测试和验证视频分辨率修复效果</p>
        
        <div class="test-section">
            <h3>🎯 快速修复</h3>
            <div class="button-group">
                <button class="test-button" onclick="applyFix()">应用修复脚本</button>
                <button class="test-button secondary" onclick="diagnoseIssues()">诊断问题</button>
                <button class="test-button" onclick="testAllPages()">测试所有页面</button>
            </div>
            <div id="fix-status" class="info-display">点击"应用修复脚本"开始修复...</div>
        </div>
        
        <div class="test-section">
            <h3>📱 设备类型强制测试</h3>
            <div class="button-group">
                <button class="test-button" onclick="forceDesktop()">强制桌面模式 (2K)</button>
                <button class="test-button secondary" onclick="forceTablet()">强制平板模式 (2K)</button>
                <button class="test-button danger" onclick="forceMobile()">强制移动模式 (1080p)</button>
                <button class="test-button" onclick="resetDeviceType()">重置自动检测</button>
            </div>
            <div id="device-status" class="info-display">当前设备类型: 检测中...</div>
        </div>
        
        <div class="test-section">
            <h3>🎥 视频预览测试</h3>
            <div class="video-container">
                <div class="video-card">
                    <h4>首页视频 (INDEX)</h4>
                    <video id="video-index" class="video-preview" controls muted></video>
                    <div id="info-index" class="info-display">未加载</div>
                </div>
                <div class="video-card">
                    <h4>相遇回忆 (MEETINGS)</h4>
                    <video id="video-meetings" class="video-preview" controls muted></video>
                    <div id="info-meetings" class="info-display">未加载</div>
                </div>
                <div class="video-card">
                    <h4>纪念日 (ANNIVERSARY)</h4>
                    <video id="video-anniversary" class="video-preview" controls muted></video>
                    <div id="info-anniversary" class="info-display">未加载</div>
                </div>
            </div>
            <div class="button-group">
                <button class="test-button" onclick="loadAllVideos()">加载所有视频</button>
                <button class="test-button secondary" onclick="clearAllVideos()">清除所有视频</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 实时日志</h3>
            <div id="log-output" class="log-output">等待操作...</div>
            <div class="button-group">
                <button class="test-button secondary" onclick="clearLog()">清除日志</button>
            </div>
        </div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="/config.js"></script>
    <script src="/scripts/fix-video-resolution.js"></script>
    
    <script>
        let logOutput = null;
        let originalConsoleLog = null;
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            logOutput = document.getElementById('log-output');
            
            // 劫持console.log以显示在页面上
            originalConsoleLog = console.log;
            console.log = function(...args) {
                originalConsoleLog.apply(console, args);
                appendLog(args.join(' '));
            };
            
            // 显示初始设备状态
            updateDeviceStatus();
            
            console.log('🔧 视频分辨率修复测试页面已加载');
        });
        
        function appendLog(message) {
            if (logOutput) {
                const timestamp = new Date().toLocaleTimeString();
                logOutput.textContent += `[${timestamp}] ${message}\n`;
                logOutput.scrollTop = logOutput.scrollHeight;
            }
        }
        
        function clearLog() {
            if (logOutput) {
                logOutput.textContent = '';
            }
        }
        
        function updateDeviceStatus() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            const userAgent = navigator.userAgent;
            
            const isMobileUA = /Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
            const isTabletUA = /iPad|Android.*Tablet|Windows.*Touch/i.test(userAgent) && !/Mobile/i.test(userAgent);
            
            const isMobile = (width <= 480) || (width <= 768 && isMobileUA && !isTabletUA);
            const isTablet = (width > 480 && width <= 1024 && isTabletUA) || 
                            (width > 768 && width <= 1024 && !isMobile);
            
            let deviceType = 'Desktop';
            let expectedResolution = '2560x1440 (2K)';
            
            if (isMobile) {
                deviceType = 'Mobile';
                expectedResolution = '1920x1080 (1080p)';
            } else if (isTablet) {
                deviceType = 'Tablet';
                expectedResolution = '2560x1440 (2K)';
            }
            
            const statusElement = document.getElementById('device-status');
            statusElement.textContent = `当前设备类型: ${deviceType}\n屏幕尺寸: ${width}x${height}\n预期视频分辨率: ${expectedResolution}\nUser Agent检测: ${isMobileUA ? 'Mobile' : isTabletUA ? 'Tablet' : 'Desktop'}`;
        }
        
        function applyFix() {
            const statusElement = document.getElementById('fix-status');
            statusElement.textContent = '正在应用修复脚本...';
            
            try {
                // 修复脚本已经在页面加载时自动运行
                // 这里我们手动触发诊断和修复
                if (typeof window.diagnoseVideoResolution === 'function') {
                    window.diagnoseVideoResolution();
                }
                
                setTimeout(() => {
                    statusElement.textContent = '✅ 修复脚本已应用\n💡 可在控制台使用以下命令:\n  - diagnoseVideoResolution()\n  - forceDeviceType("desktop")\n  - forceDeviceType("mobile")';
                    console.log('✅ 修复脚本应用完成');
                }, 1000);
                
            } catch (error) {
                statusElement.textContent = `❌ 修复失败: ${error.message}`;
                console.error('❌ 修复失败:', error);
            }
        }
        
        function diagnoseIssues() {
            console.log('🔍 开始诊断视频分辨率问题...');
            
            if (typeof window.diagnoseVideoResolution === 'function') {
                window.diagnoseVideoResolution();
            } else {
                console.log('⚠️ 诊断函数未找到，请先应用修复脚本');
            }
        }
        
        function forceDesktop() {
            if (typeof window.forceDeviceType === 'function') {
                window.forceDeviceType('desktop');
                updateDeviceStatus();
                console.log('🖥️ 已强制桌面模式，预期分辨率: 2560x1440');
            } else {
                console.log('⚠️ 强制设备类型函数未找到，请先应用修复脚本');
            }
        }
        
        function forceTablet() {
            if (typeof window.forceDeviceType === 'function') {
                window.forceDeviceType('tablet');
                updateDeviceStatus();
                console.log('📱 已强制平板模式，预期分辨率: 2560x1440');
            } else {
                console.log('⚠️ 强制设备类型函数未找到，请先应用修复脚本');
            }
        }
        
        function forceMobile() {
            if (typeof window.forceDeviceType === 'function') {
                window.forceDeviceType('mobile');
                updateDeviceStatus();
                console.log('📱 已强制移动模式，预期分辨率: 1920x1080');
            } else {
                console.log('⚠️ 强制设备类型函数未找到，请先应用修复脚本');
            }
        }
        
        function resetDeviceType() {
            window._forcedDeviceType = undefined;
            updateDeviceStatus();
            console.log('🔄 已重置为自动设备检测');
        }
        
        function loadAllVideos() {
            const pages = [
                { key: 'INDEX', id: 'video-index', name: '首页' },
                { key: 'MEETINGS', id: 'video-meetings', name: '相遇回忆' },
                { key: 'ANNIVERSARY', id: 'video-anniversary', name: '纪念日' }
            ];
            
            pages.forEach(page => {
                loadVideoForPage(page.key, page.id, page.name);
            });
        }
        
        function loadVideoForPage(pageKey, videoId, pageName) {
            const video = document.getElementById(videoId);
            const info = document.getElementById(`info-${pageKey.toLowerCase()}`);
            
            if (!video || !info) return;
            
            console.log(`🎬 加载${pageName}视频...`);
            
            // 生成正确的视频URL
            const url = generateVideoUrl(pageKey);
            
            if (url) {
                video.src = url;
                info.textContent = `加载中...\nURL: ${url}`;
                
                video.addEventListener('loadedmetadata', function() {
                    const resolution = `${video.videoWidth}x${video.videoHeight}`;
                    info.textContent = `✅ 已加载\n分辨率: ${resolution}\nURL: ${url}`;
                    console.log(`✅ ${pageName}视频加载成功，分辨率: ${resolution}`);
                });
                
                video.addEventListener('error', function(e) {
                    info.textContent = `❌ 加载失败\n错误: ${e.message || '未知错误'}\nURL: ${url}`;
                    console.error(`❌ ${pageName}视频加载失败:`, e);
                });
                
            } else {
                info.textContent = '❌ 无法生成视频URL';
                console.error(`❌ 无法为${pageName}生成视频URL`);
            }
        }
        
        function generateVideoUrl(pageKey) {
            const baseUrls = {
                'INDEX': 'https://res.cloudinary.com/dcglebc2w/video/upload',
                'MEETINGS': 'https://res.cloudinary.com/dkqnm9nwr/video/upload',
                'ANNIVERSARY': 'https://res.cloudinary.com/drhqbbqxz/video/upload'
            };
            
            const publicIds = {
                'INDEX': 'love-website/home',
                'MEETINGS': 'love-website/meetings',
                'ANNIVERSARY': 'love-website/anniversary'
            };
            
            const baseUrl = baseUrls[pageKey];
            const publicId = publicIds[pageKey];
            
            if (!baseUrl || !publicId) return null;
            
            // 使用修复后的设备检测
            const width = window.innerWidth;
            const userAgent = navigator.userAgent;
            const isMobileUA = /Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
            const isTabletUA = /iPad|Android.*Tablet|Windows.*Touch/i.test(userAgent) && !/Mobile/i.test(userAgent);
            
            const isMobile = (width <= 480) || (width <= 768 && isMobileUA && !isTabletUA);
            const isTablet = (width > 480 && width <= 1024 && isTabletUA) || 
                            (width > 768 && width <= 1024 && !isMobile);
            
            let transforms;
            if (isMobile) {
                transforms = 'q_95,f_auto,w_1920,h_1080,c_limit,fl_immutable_cache';
            } else if (isTablet) {
                transforms = 'q_100,f_auto,w_2560,h_1440,c_limit,fl_immutable_cache';
            } else {
                transforms = 'q_100,f_auto,w_2560,h_1440,c_limit,fl_immutable_cache';
            }
            
            return `${baseUrl}/${transforms}/${publicId}.mp4`;
        }
        
        function clearAllVideos() {
            const videoIds = ['video-index', 'video-meetings', 'video-anniversary'];
            const infoIds = ['info-index', 'info-meetings', 'info-anniversary'];
            
            videoIds.forEach(id => {
                const video = document.getElementById(id);
                if (video) {
                    video.src = '';
                    video.load();
                }
            });
            
            infoIds.forEach(id => {
                const info = document.getElementById(id);
                if (info) {
                    info.textContent = '已清除';
                }
            });
            
            console.log('🗑️ 已清除所有视频');
        }
        
        function testAllPages() {
            console.log('🧪 开始测试所有页面的视频URL生成...');
            
            const pages = ['INDEX', 'MEETINGS', 'ANNIVERSARY', 'MEMORIAL', 'TOGETHER_DAYS'];
            
            pages.forEach(page => {
                const url = generateVideoUrl(page);
                if (url) {
                    console.log(`✅ ${page}: ${url}`);
                    
                    // 分析URL中的分辨率参数
                    const resMatch = url.match(/w_(\d+),h_(\d+)/);
                    if (resMatch) {
                        console.log(`   分辨率: ${resMatch[1]}x${resMatch[2]}`);
                    }
                } else {
                    console.log(`❌ ${page}: 无法生成URL`);
                }
            });
        }
        
        // 监听窗口大小变化
        window.addEventListener('resize', function() {
            setTimeout(updateDeviceStatus, 100);
        });
    </script>
</body>
</html>
