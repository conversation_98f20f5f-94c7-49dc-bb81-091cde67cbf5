#!/bin/bash

# Love网站系统诊断脚本
# 全面诊断视频加载、API调用和脚本引用问题

echo "🔍 Love网站系统诊断开始"
echo "=========================="
echo "时间: $(date)"
echo "域名: love.yuh.cool"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 测试计数
total_tests=0
passed_tests=0
failed_tests=0

# 测试结果记录
test_result() {
    ((total_tests++))
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
        ((passed_tests++))
    else
        echo -e "${RED}❌ $2${NC}"
        ((failed_tests++))
        if [ ! -z "$3" ]; then
            echo -e "${YELLOW}   详情: $3${NC}"
        fi
    fi
}

# 1. 测试主要页面可访问性
echo -e "${BLUE}📊 1. 页面可访问性测试${NC}"
echo "================================"

pages=("" "anniversary" "meetings" "memorial" "together-days")
page_names=("主页" "纪念日" "相遇回忆" "纪念物" "在一起的日子")

for i in "${!pages[@]}"; do
    page="${pages[$i]}"
    name="${page_names[$i]}"
    url="https://love.yuh.cool/${page}"
    
    status=$(curl -s -o /dev/null -w "%{http_code}" "$url" --max-time 10)
    response_time=$(curl -s -o /dev/null -w "%{time_total}" "$url" --max-time 10)
    
    if [ "$status" = "200" ]; then
        test_result 0 "$name 页面可访问 (${status}, ${response_time}s)"
    else
        test_result 1 "$name 页面访问失败" "HTTP状态码: $status"
    fi
done

# 2. 测试关键脚本文件
echo -e "\n${BLUE}📊 2. 关键脚本文件测试${NC}"
echo "================================"

scripts=(
    "config.js"
    "simple-video-manager.js"
    "hybrid-cdn-manager.js"
    "cloudinary-load-balancer.js"
    "script.js"
    "style.css"
)

for script in "${scripts[@]}"; do
    url="https://love.yuh.cool/${script}"
    status=$(curl -s -o /dev/null -w "%{http_code}" "$url" --max-time 10)
    size=$(curl -s -w "%{size_download}" -o /dev/null "$url" --max-time 10)
    
    if [ "$status" = "200" ] && [ "$size" -gt 0 ]; then
        test_result 0 "$script 可访问 (${size} bytes)"
    else
        test_result 1 "$script 访问失败" "HTTP: $status, Size: $size"
    fi
done

# 3. 测试API端点
echo -e "\n${BLUE}📊 3. API端点测试${NC}"
echo "========================="

api_endpoints=(
    "/api/health"
    "/api/messages"
    "/api/together-days"
)

for endpoint in "${api_endpoints[@]}"; do
    url="https://love.yuh.cool${endpoint}"
    
    # 测试响应状态和内容类型
    response=$(curl -s -w "HTTPSTATUS:%{http_code};CONTENT_TYPE:%{content_type};TIME:%{time_total}" "$url" --max-time 10)
    
    # 提取状态码
    status=$(echo "$response" | grep -o "HTTPSTATUS:[0-9]*" | cut -d: -f2)
    content_type=$(echo "$response" | grep -o "CONTENT_TYPE:[^;]*" | cut -d: -f2)
    time=$(echo "$response" | grep -o "TIME:[^;]*" | cut -d: -f2)
    
    # 提取响应体
    body=$(echo "$response" | sed -E 's/HTTPSTATUS:[0-9]*;CONTENT_TYPE:[^;]*;TIME:[^;]*$//')
    
    if [ "$status" = "200" ] && [[ "$content_type" == *"json"* ]]; then
        test_result 0 "$endpoint API正常 (${time}s)"
        
        # 检查JSON格式
        if echo "$body" | python3 -m json.tool > /dev/null 2>&1; then
            echo -e "${GREEN}   JSON格式正确${NC}"
        else
            echo -e "${YELLOW}   JSON格式可能有问题${NC}"
        fi
    else
        test_result 1 "$endpoint API异常" "HTTP: $status, Type: $content_type"
    fi
done

# 4. 测试Cloudinary视频资源
echo -e "\n${BLUE}📊 4. Cloudinary视频资源测试${NC}"
echo "====================================="

cloudinary_videos=(
    "home"
    "meetings"
    "anniversary"
    "memorial"
    "together-days"
)

for video in "${cloudinary_videos[@]}"; do
    url="https://res.cloudinary.com/dcglebc2w/video/upload/love-website/${video}.mp4"
    
    # 测试HEAD请求以获取文件信息
    status=$(curl -s -I "$url" -w "%{http_code}" -o /dev/null --max-time 15)
    
    if [ "$status" = "200" ]; then
        # 获取文件大小
        size=$(curl -s -I "$url" | grep -i content-length | cut -d' ' -f2 | tr -d '\r')
        size_mb=$((size / 1024 / 1024))
        test_result 0 "Cloudinary ${video}.mp4 可访问 (${size_mb}MB)"
    else
        test_result 1 "Cloudinary ${video}.mp4 访问失败" "HTTP状态码: $status"
    fi
done

# 5. 测试本地压缩视频文件
echo -e "\n${BLUE}📊 5. 本地压缩视频文件测试${NC}"
echo "====================================="

local_videos=(
    "home.mp4"
    "meetings.mp4"
    "anniversary.mp4"
    "memorial.mp4"
    "together-days.mp4"
)

for video in "${local_videos[@]}"; do
    url="https://love.yuh.cool/background/cloudinary-ready/${video}"
    status=$(curl -s -I "$url" -w "%{http_code}" -o /dev/null --max-time 15)
    
    if [ "$status" = "200" ]; then
        size=$(curl -s -I "$url" | grep -i content-length | cut -d' ' -f2 | tr -d '\r')
        size_mb=$((size / 1024 / 1024))
        test_result 0 "本地 ${video} 可访问 (${size_mb}MB)"
    else
        test_result 1 "本地 ${video} 访问失败" "HTTP状态码: $status"
    fi
done

echo -e "\n${BLUE}📊 6. 页面脚本引用分析${NC}"
echo "==============================="

# 分析各页面的脚本引用
for page in "${pages[@]}"; do
    if [ -z "$page" ]; then
        continue
    fi
    
    name="${page_names[$((${#pages[@]} - ${#pages[@]#*$page}))]}"
    url="https://love.yuh.cool/${page}"
    
    echo -e "\n${YELLOW}分析 ${name} 页面脚本引用:${NC}"
    
    # 获取页面内容并分析脚本引用
    page_content=$(curl -s "$url" --max-time 10)
    
    if [ $? -eq 0 ]; then
        # 检查关键脚本引用
        if echo "$page_content" | grep -q "simple-video-manager.js"; then
            echo -e "${GREEN}  ✅ 引用了 simple-video-manager.js${NC}"
        else
            echo -e "${RED}  ❌ 未引用 simple-video-manager.js${NC}"
        fi
        
        if echo "$page_content" | grep -q "hybrid-cdn-manager.js"; then
            echo -e "${GREEN}  ✅ 引用了 hybrid-cdn-manager.js${NC}"
        else
            echo -e "${RED}  ❌ 未引用 hybrid-cdn-manager.js${NC}"
        fi
        
        if echo "$page_content" | grep -q "config.js"; then
            echo -e "${GREEN}  ✅ 引用了 config.js${NC}"
        else
            echo -e "${RED}  ❌ 未引用 config.js${NC}"
        fi
        
        # 检查过时的脚本引用
        if echo "$page_content" | grep -q "video-manager.js"; then
            echo -e "${YELLOW}  ⚠️  仍在使用过时的 video-manager.js${NC}"
        fi

    else
        echo -e "${RED}  ❌ 无法获取页面内容${NC}"
    fi
done

# 7. 生成诊断报告
echo -e "\n${BLUE}📊 诊断报告生成${NC}"
echo "======================"

echo -e "\n总测试项: $total_tests"
echo -e "通过测试: ${GREEN}$passed_tests${NC}"
echo -e "失败测试: ${RED}$failed_tests${NC}"

success_rate=$((passed_tests * 100 / total_tests))
echo -e "成功率: $success_rate%"

# 生成问题清单
echo -e "\n${YELLOW}🔧 发现的问题和建议:${NC}"
echo "=========================="

if [ $failed_tests -gt 0 ]; then
    echo "1. 脚本引用问题:"
    echo "   - 部分子页面可能未正确引用所需的CDN管理器脚本"
    echo "   - 可能存在过时的video-manager.js引用"
    echo ""
    echo "2. 视频加载问题:"
    echo "   - 检查Cloudinary和本地视频文件的可访问性"
    echo "   - 验证CDN选择逻辑是否正确工作"
    echo ""
    echo "3. API调用问题:"
    echo "   - 验证API端点的响应格式和数据完整性"
    echo "   - 检查前端JavaScript的API调用逻辑"
    echo ""
    echo "4. 性能问题:"
    echo "   - 页面加载时间和资源大小需要优化"
    echo "   - 需要实现缓存和预加载机制"
else
    echo -e "${GREEN}✅ 系统基础功能正常，可以进行下一步优化${NC}"
fi

echo -e "\n${BLUE}📋 下一步行动建议:${NC}"
echo "===================="
echo "1. 修复子页面脚本引用和加载顺序"
echo "2. 优化VideoManager初始化逻辑"
echo "3. 实现视频缓存机制"
echo "4. 修复留言板API调用问题"
echo "5. 优化页面切换性能"

echo -e "\n🔍 诊断完成 - $(date)"
echo "详细日志已保存，可用于后续问题修复参考"
