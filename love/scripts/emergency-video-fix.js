/**
 * 紧急视频修复脚本
 * 解决CDN管理器初始化失败和Cloudinary URL错误问题
 */

(function() {
    'use strict';
    
    console.log('🚨 紧急视频修复脚本启动...');
    
    // 修复后的视频URL生成函数
    function generateWorkingVideoUrl(pageKey) {
        // 直接使用工作的Cloudinary URL，绕过复杂的CDN管理器
        const videoConfigs = {
            'INDEX': {
                cloudName: 'dcglebc2w',
                publicId: 'love-website/home'
            },
            'MEETINGS': {
                cloudName: 'dkqnm9nwr', 
                publicId: 'love-website/meetings'
            },
            'ANNIVERSARY': {
                cloudName: 'drhqbbqxz',
                publicId: 'love-website/anniversary'
            },
            'MEMORIAL': {
                cloudName: 'ds14sv2gh',
                publicId: 'love-website/memorial'
            },
            'TOGETHER_DAYS': {
                cloudName: 'dpq95x5nf',
                publicId: 'love-website/together-days'
            }
        };
        
        const config = videoConfigs[pageKey];
        if (!config) {
            console.error(`❌ 未找到页面 ${pageKey} 的配置`);
            return null;
        }
        
        // 使用简单的设备检测
        const screenWidth = window.innerWidth;
        const userAgent = navigator.userAgent;
        const isMobileUA = /Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
        
        // 更保守的移动设备检测
        const isMobile = screenWidth <= 480 || (screenWidth <= 768 && isMobileUA);
        
        // 根据设备选择变换参数
        let transforms;
        if (isMobile) {
            transforms = 'q_95,f_auto,w_1920,h_1080,c_limit,fl_immutable_cache';
            console.log(`📱 ${pageKey}: 使用移动设备高清质量 (1920x1080)`);
        } else {
            transforms = 'q_100,f_auto,w_2560,h_1440,c_limit,fl_immutable_cache';
            console.log(`🖥️ ${pageKey}: 使用桌面设备2K质量 (2560x1440)`);
        }
        
        const url = `https://res.cloudinary.com/${config.cloudName}/video/upload/${transforms}/${config.publicId}.mp4`;
        console.log(`📹 生成视频URL: ${url}`);
        
        return url;
    }
    
    // 创建简化的VideoManager
    function createEmergencyVideoManager() {
        return {
            isInitialized: true,
            
            async loadVideo(pageKey, videoConfig) {
                console.log(`🎬 紧急加载视频: ${pageKey}`);
                
                const video = document.createElement('video');
                video.autoplay = true;
                video.muted = true;
                video.loop = true;
                video.playsInline = true;
                video.preload = 'auto';
                video.style.opacity = '0';
                video.style.transition = 'opacity 1s ease-in-out';
                
                const url = generateWorkingVideoUrl(pageKey);
                if (!url) {
                    throw new Error(`无法生成${pageKey}的视频URL`);
                }
                
                return new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => {
                        reject(new Error('视频加载超时'));
                    }, 15000);
                    
                    video.addEventListener('canplaythrough', () => {
                        clearTimeout(timeout);
                        video.style.opacity = '1';
                        console.log(`✅ ${pageKey}视频加载成功`);
                        resolve(video);
                    });
                    
                    video.addEventListener('error', (e) => {
                        clearTimeout(timeout);
                        console.error(`❌ ${pageKey}视频加载失败:`, e);
                        reject(new Error(`视频加载失败: ${e.message || '未知错误'}`));
                    });
                    
                    video.src = url;
                    video.load();
                });
            },
            
            pauseAllVideos() {
                const videos = document.querySelectorAll('video');
                videos.forEach(video => {
                    if (!video.paused) {
                        video.pause();
                    }
                });
            },
            
            resumeCurrentVideo() {
                const videos = document.querySelectorAll('video');
                videos.forEach(video => {
                    if (video.paused) {
                        video.play().catch(e => {
                            console.log('自动播放被阻止');
                        });
                    }
                });
            },
            
            startPreloading() {
                console.log('📦 预加载功能已禁用（紧急模式）');
                return Promise.resolve();
            },
            
            cleanupPreloadCache() {
                console.log('🗑️ 缓存清理功能已禁用（紧急模式）');
            }
        };
    }
    
    // 立即修复当前页面的视频
    function fixCurrentPageVideo() {
        const videoElements = document.querySelectorAll('video');
        
        if (videoElements.length === 0) {
            console.log('⚠️ 未找到视频元素');
            return;
        }
        
        // 检测当前页面
        const path = window.location.pathname;
        let pageKey = 'INDEX';
        
        if (path.includes('meetings')) pageKey = 'MEETINGS';
        else if (path.includes('anniversary')) pageKey = 'ANNIVERSARY';
        else if (path.includes('memorial')) pageKey = 'MEMORIAL';
        else if (path.includes('together-days')) pageKey = 'TOGETHER_DAYS';
        
        console.log(`📄 当前页面: ${pageKey}`);
        
        const correctUrl = generateWorkingVideoUrl(pageKey);
        
        if (!correctUrl) {
            console.error('❌ 无法生成正确的视频URL');
            return;
        }
        
        videoElements.forEach((video, index) => {
            if (video.src !== correctUrl) {
                console.log(`🔄 修复视频 ${index + 1}:`);
                console.log(`   原URL: ${video.src}`);
                console.log(`   新URL: ${correctUrl}`);
                
                video.src = correctUrl;
                video.load();
                
                video.addEventListener('canplaythrough', () => {
                    video.style.opacity = '1';
                    video.play().catch(e => {
                        console.log('自动播放被阻止，等待用户交互');
                    });
                });
                
                console.log(`✅ 视频 ${index + 1} 已修复`);
            }
        });
    }
    
    // 主修复函数
    function applyEmergencyFix() {
        console.log('🚨 应用紧急修复...');
        
        // 1. 创建紧急VideoManager
        if (!window.VideoManager || !window.VideoManager.isInitialized) {
            console.log('🔧 创建紧急VideoManager...');
            window.VideoManager = createEmergencyVideoManager();
            
            // 触发就绪事件
            window.dispatchEvent(new CustomEvent('videoManagerReady', {
                detail: { videoManager: window.VideoManager }
            }));
            
            console.log('✅ 紧急VideoManager已创建');
        }
        
        // 2. 修复当前页面视频
        setTimeout(() => {
            fixCurrentPageVideo();
        }, 1000);
        
        // 3. 提供手动修复函数
        window.emergencyVideoFix = fixCurrentPageVideo;
        window.generateWorkingVideoUrl = generateWorkingVideoUrl;
        
        console.log('✅ 紧急修复已应用');
        console.log('💡 可用命令:');
        console.log('  - emergencyVideoFix(): 手动修复当前页面视频');
        console.log('  - generateWorkingVideoUrl("INDEX"): 生成指定页面的视频URL');
    }
    
    // 等待DOM加载完成后应用修复
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', applyEmergencyFix);
    } else {
        applyEmergencyFix();
    }
    
    // 也可以立即应用部分修复
    if (!window.VideoManager || !window.VideoManager.isInitialized) {
        window.VideoManager = createEmergencyVideoManager();
        console.log('⚡ 立即创建紧急VideoManager');
    }
    
})();
