<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>紧急修复测试 - Love Website</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #fff;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .test-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .test-button.danger {
            background: linear-gradient(45deg, #F44336, #D32F2F);
        }
        
        .video-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .video-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .video-card h4 {
            margin-top: 0;
            color: #FFD700;
        }
        
        .video-preview {
            width: 100%;
            height: 250px;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        .info-display {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            margin: 10px 0;
            white-space: pre-wrap;
            max-height: 150px;
            overflow-y: auto;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-good { background-color: #4CAF50; }
        .status-warning { background-color: #FF9800; }
        .status-error { background-color: #F44336; }
        
        .log-output {
            background: rgba(0, 0, 0, 0.5);
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 紧急视频修复测试</h1>
        <p style="text-align: center; opacity: 0.8;">测试紧急修复脚本的效果</p>
        
        <div class="test-section">
            <h3>📊 系统状态检查</h3>
            <div id="system-status" class="info-display">检查中...</div>
            <div class="button-group">
                <button class="test-button" onclick="checkSystemStatus()">刷新状态</button>
                <button class="test-button danger" onclick="applyEmergencyFix()">应用紧急修复</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🎥 视频URL测试</h3>
            <div class="button-group">
                <button class="test-button" onclick="testVideoUrls()">测试所有URL</button>
                <button class="test-button" onclick="testCurrentPageVideo()">测试当前页面</button>
            </div>
            <div id="url-test-results" class="info-display">点击按钮开始测试...</div>
        </div>
        
        <div class="test-section">
            <h3>🎬 视频加载测试</h3>
            <div class="video-container">
                <div class="video-card">
                    <h4>首页视频测试</h4>
                    <video id="test-video-index" class="video-preview" controls muted></video>
                    <div id="info-index" class="info-display">未加载</div>
                    <button class="test-button" onclick="loadTestVideo('INDEX', 'test-video-index', 'info-index')">加载测试</button>
                </div>
                <div class="video-card">
                    <h4>相遇回忆视频测试</h4>
                    <video id="test-video-meetings" class="video-preview" controls muted></video>
                    <div id="info-meetings" class="info-display">未加载</div>
                    <button class="test-button" onclick="loadTestVideo('MEETINGS', 'test-video-meetings', 'info-meetings')">加载测试</button>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📋 实时日志</h3>
            <div id="log-output" class="log-output">等待操作...</div>
            <button class="test-button" onclick="clearLog()">清除日志</button>
        </div>
    </div>

    <!-- 加载紧急修复脚本 -->
    <script src="/config.js"></script>
    <script src="/scripts/emergency-video-fix.js"></script>
    
    <script>
        let logOutput = null;
        let originalConsoleLog = null;
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            logOutput = document.getElementById('log-output');
            
            // 劫持console.log以显示在页面上
            originalConsoleLog = console.log;
            console.log = function(...args) {
                originalConsoleLog.apply(console, args);
                appendLog(args.join(' '));
            };
            
            console.log('🚨 紧急修复测试页面已加载');
            
            // 自动检查系统状态
            setTimeout(checkSystemStatus, 1000);
        });
        
        function appendLog(message) {
            if (logOutput) {
                const timestamp = new Date().toLocaleTimeString();
                logOutput.textContent += `[${timestamp}] ${message}\n`;
                logOutput.scrollTop = logOutput.scrollHeight;
            }
        }
        
        function clearLog() {
            if (logOutput) {
                logOutput.textContent = '';
            }
        }
        
        function checkSystemStatus() {
            const statusElement = document.getElementById('system-status');
            let status = '';
            
            // 检查CONFIG
            if (window.CONFIG) {
                status += '✅ CONFIG已加载\n';
                if (window.CONFIG.CLOUDINARY) {
                    status += `✅ Cloudinary配置存在 (启用: ${window.CONFIG.CLOUDINARY.ENABLED})\n`;
                } else {
                    status += '❌ Cloudinary配置缺失\n';
                }
            } else {
                status += '❌ CONFIG未加载\n';
            }
            
            // 检查VideoManager
            if (window.VideoManager) {
                status += `✅ VideoManager存在 (初始化: ${window.VideoManager.isInitialized})\n`;
            } else {
                status += '❌ VideoManager不存在\n';
            }
            
            // 检查紧急修复函数
            if (window.emergencyVideoFix) {
                status += '✅ 紧急修复函数可用\n';
            } else {
                status += '❌ 紧急修复函数不可用\n';
            }
            
            if (window.generateWorkingVideoUrl) {
                status += '✅ URL生成函数可用\n';
            } else {
                status += '❌ URL生成函数不可用\n';
            }
            
            // 设备信息
            const width = window.innerWidth;
            const userAgent = navigator.userAgent;
            const isMobileUA = /Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
            const isMobile = width <= 480 || (width <= 768 && isMobileUA);
            
            status += `\n📱 设备信息:\n`;
            status += `屏幕宽度: ${width}px\n`;
            status += `UA检测: ${isMobileUA ? 'Mobile' : 'Desktop'}\n`;
            status += `最终判断: ${isMobile ? 'Mobile (1080p)' : 'Desktop (2K)'}\n`;
            
            statusElement.textContent = status;
            console.log('📊 系统状态检查完成');
        }
        
        function applyEmergencyFix() {
            console.log('🚨 手动应用紧急修复...');
            
            if (window.emergencyVideoFix) {
                window.emergencyVideoFix();
                console.log('✅ 紧急修复已应用');
            } else {
                console.error('❌ 紧急修复函数不可用');
            }
            
            // 刷新状态
            setTimeout(checkSystemStatus, 1000);
        }
        
        function testVideoUrls() {
            const resultsElement = document.getElementById('url-test-results');
            let results = '🧪 视频URL测试结果:\n\n';
            
            const pages = ['INDEX', 'MEETINGS', 'ANNIVERSARY', 'MEMORIAL', 'TOGETHER_DAYS'];
            
            pages.forEach(page => {
                if (window.generateWorkingVideoUrl) {
                    const url = window.generateWorkingVideoUrl(page);
                    if (url) {
                        results += `✅ ${page}:\n${url}\n\n`;
                        
                        // 分析URL参数
                        const resMatch = url.match(/w_(\d+),h_(\d+)/);
                        if (resMatch) {
                            results += `   分辨率: ${resMatch[1]}x${resMatch[2]}\n`;
                        }
                        
                        const qualityMatch = url.match(/q_(\d+)/);
                        if (qualityMatch) {
                            results += `   质量: ${qualityMatch[1]}\n`;
                        }
                        results += '\n';
                    } else {
                        results += `❌ ${page}: 无法生成URL\n\n`;
                    }
                } else {
                    results += `❌ ${page}: URL生成函数不可用\n\n`;
                }
            });
            
            resultsElement.textContent = results;
            console.log('🧪 视频URL测试完成');
        }
        
        function testCurrentPageVideo() {
            console.log('🎬 测试当前页面视频...');
            
            if (window.emergencyVideoFix) {
                window.emergencyVideoFix();
            } else {
                console.error('❌ 紧急修复函数不可用');
            }
        }
        
        function loadTestVideo(pageKey, videoId, infoId) {
            const video = document.getElementById(videoId);
            const info = document.getElementById(infoId);
            
            if (!video || !info) {
                console.error(`❌ 找不到视频元素: ${videoId} 或信息元素: ${infoId}`);
                return;
            }
            
            console.log(`🎬 加载测试视频: ${pageKey}`);
            
            if (!window.generateWorkingVideoUrl) {
                info.textContent = '❌ URL生成函数不可用';
                console.error('❌ URL生成函数不可用');
                return;
            }
            
            const url = window.generateWorkingVideoUrl(pageKey);
            if (!url) {
                info.textContent = '❌ 无法生成视频URL';
                console.error(`❌ 无法生成${pageKey}的视频URL`);
                return;
            }
            
            info.textContent = `加载中...\nURL: ${url}`;
            
            video.addEventListener('loadedmetadata', function() {
                const resolution = `${video.videoWidth}x${video.videoHeight}`;
                info.textContent = `✅ 加载成功\n分辨率: ${resolution}\nURL: ${url}`;
                console.log(`✅ ${pageKey}视频加载成功，分辨率: ${resolution}`);
            }, { once: true });
            
            video.addEventListener('error', function(e) {
                info.textContent = `❌ 加载失败\n错误: ${e.message || '未知错误'}\nURL: ${url}`;
                console.error(`❌ ${pageKey}视频加载失败:`, e);
            }, { once: true });
            
            video.src = url;
            video.load();
        }
    </script>
</body>
</html>
