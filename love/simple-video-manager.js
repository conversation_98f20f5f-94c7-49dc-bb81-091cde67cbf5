/**
 * Simple Video Manager - 简单直接的视频管理器
 * 完全删除复杂优化策略，直接加载视频
 */

class SimpleVideoManager {
    constructor() {
        console.log('🚀 Simple Video Manager initialized - Enhanced with CDN Load Balancing');
        this.currentVideo = null;
        this.initializationPromise = null;
        this.isInitialized = false;
        this.preloadManager = null;

        // 异步初始化CDN管理器
        this.initializationPromise = this.initializeCDNManagers();
    }

    // 异步初始化CDN管理器
    async initializeCDNManagers() {
        try {
            // 等待依赖脚本加载完成
            await this.waitForDependencies();

            // 检查是否启用Cloudinary
            this.cloudinaryEnabled = window.CONFIG?.CLOUDINARY?.ENABLED || false;
            console.log(`🔧 Cloudinary启用状态: ${this.cloudinaryEnabled}`);

            // 优先使用混合CDN管理器
            if (window.HybridCDNManager) {
                this.cdnManager = new window.HybridCDNManager();
                console.log('🔄 混合CDN管理器已启用');
                this.isInitialized = true;
                return;
            }

            // 备选：负载均衡器
            if (window.CloudinaryLoadBalancer && this.cloudinaryEnabled) {
                this.cdnManager = new window.CloudinaryLoadBalancer();
                console.log('⚖️ Cloudinary负载均衡器已启用');
                this.isInitialized = true;
                return;
            }

            // 备选：原始Cloudinary管理器
            if (window.CloudinaryVideoManager && this.cloudinaryEnabled) {
                this.cdnManager = new window.CloudinaryVideoManager(
                    window.CONFIG.CLOUDINARY.CLOUD_NAME
                );
                console.log('☁️ 基础Cloudinary管理器已启用');
                this.isInitialized = true;
                return;
            }

            console.log('📁 使用本地文件模式');
            this.cdnManager = null;
            this.isInitialized = true;

        } catch (error) {
            console.error('❌ CDN管理器初始化失败:', error);
            this.cdnManager = null;
            this.isInitialized = true;
        }

        // 初始化预加载管理器
        this.initializePreloadManager();
    }

    /**
     * 初始化预加载管理器
     */
    initializePreloadManager() {
        try {
            if (window.PreloadManager) {
                this.preloadManager = new window.PreloadManager(this);
                console.log('🎯 预加载管理器已初始化');
            } else {
                console.warn('⚠️ PreloadManager类未找到，预加载功能不可用');
            }
        } catch (error) {
            console.error('❌ 预加载管理器初始化失败:', error);
        }
    }

    // 等待依赖脚本加载完成
    async waitForDependencies(maxWaitTime = 5000) {
        const startTime = Date.now();

        while (Date.now() - startTime < maxWaitTime) {
            // 检查必需的依赖是否已加载
            if (window.CONFIG &&
                (window.HybridCDNManager || window.CloudinaryLoadBalancer || window.CloudinaryVideoManager)) {
                console.log('✅ 依赖脚本加载完成');
                return;
            }

            // 等待50ms后重试
            await new Promise(resolve => setTimeout(resolve, 50));
        }

        console.warn('⚠️ 部分依赖脚本可能未完全加载，继续初始化');
    }

    /**
     * 智能加载视频 - 支持多CDN负载均衡和预加载
     */
    async loadVideo(pageKey, videoConfig, options = {}) {
        console.log(`🎬 Loading video for ${pageKey}:`, videoConfig.name);

        try {
            // 等待初始化完成
            if (!this.isInitialized) {
                console.log('⏳ 等待VideoManager初始化完成...');
                await this.initializationPromise;
            }

            // 检查是否有预加载的视频
            if (this.preloadManager && !options.skipPreload) {
                const preloadedVideo = this.preloadManager.getPreloadedVideo(pageKey);
                if (preloadedVideo) {
                    console.log(`🎯 使用预加载视频: ${pageKey}`);
                    this.currentVideo = preloadedVideo;
                    return preloadedVideo;
                }
            }
            // 创建视频元素
            const video = document.createElement('video');
            video.autoplay = true;
            video.muted = true;
            video.loop = true;
            video.playsInline = true;
            video.preload = 'auto';

            // 使用CDN管理器（如果可用）
            if (this.cdnManager) {
                console.log(`🔄 Using CDN Manager for ${pageKey}`);

                // 分析用户上下文
                const userContext = this.getUserContext();

                let result;
                if (this.cdnManager.loadVideoBackground) {
                    // 混合CDN管理器或负载均衡器
                    result = await this.cdnManager.loadVideoBackground(pageKey, video, userContext);
                } else if (this.cdnManager.loadVideo) {
                    // 原始Cloudinary管理器
                    result = await this.cdnManager.loadVideo(pageKey, videoConfig);
                }

                if (result === true || (result && result.success)) {
                    this.currentVideo = video;
                    console.log(`✅ CDN video loaded successfully for ${pageKey}`);
                    return video;
                }
                console.log(`⚠️ CDN loading failed, falling back to local file`);
            }

            // 回退到本地文件
            console.log(`📁 Using local file: ${videoConfig.url}`);
            video.src = videoConfig.url;

            // 等待视频加载
            await this.waitForVideoLoad(video);

            this.currentVideo = video;
            console.log(`✅ Local video loaded successfully for ${pageKey}`);
            return video;

        } catch (error) {
            console.error(`❌ Failed to load video for ${pageKey}:`, error);
            throw error;
        }
    }

    /**
     * 获取用户上下文信息
     */
    getUserContext() {
        // 安全获取window属性
        const screenWidth = (typeof window !== 'undefined' && window.innerWidth) || 1920;
        const screenHeight = (typeof window !== 'undefined' && window.innerHeight) || 1080;
        const pixelRatio = (typeof window !== 'undefined' && window.devicePixelRatio) || 1;

        // 安全获取navigator属性
        const hasNavigator = typeof navigator !== 'undefined';
        const connection = hasNavigator ? navigator.connection : null;
        const userAgent = hasNavigator ? navigator.userAgent : 'Unknown';

        // 改进的设备检测逻辑
        const isMobileUA = /Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
        const isTabletUA = /iPad|Android.*Tablet|Windows.*Touch/i.test(userAgent) && !/Mobile/i.test(userAgent);

        // 更严格的移动设备检测：同时满足屏幕大小和User Agent
        const isMobile = (screenWidth <= 480) || (screenWidth <= 768 && isMobileUA && !isTabletUA);
        const isTablet = (screenWidth > 480 && screenWidth <= 1024 && isTabletUA) ||
                        (screenWidth > 768 && screenWidth <= 1024 && !isMobile);

        // 添加调试日志
        console.log(`🔍 设备检测: 屏幕=${screenWidth}x${screenHeight}, UA检测=${isMobileUA ? 'Mobile' : 'Desktop'}, 最终判断=${isMobile ? 'Mobile' : isTablet ? 'Tablet' : 'Desktop'}`);

        return {
            isMobile: isMobile,
            isTablet: isTablet,
            isSlowConnection: connection &&
                (connection.effectiveType === 'slow-2g' ||
                 connection.effectiveType === '2g'),
            userAgent: userAgent,
            screenWidth: screenWidth,
            screenHeight: screenHeight,
            pixelRatio: pixelRatio
        };
    }

    /**
     * 等待视频加载完成
     */
    waitForVideoLoad(video) {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error('Video load timeout after 30 seconds'));
            }, 30000);

            video.addEventListener('loadeddata', () => {
                clearTimeout(timeout);
                console.log('✅ Video loadeddata event fired');
                resolve(video);
            });

            video.addEventListener('error', (e) => {
                clearTimeout(timeout);
                console.error('❌ Video error event:', e);
                reject(new Error(`Video load error: ${e.message || 'Unknown error'}`));
            });

            // 开始加载
            video.load();
        });
    }

    /**
     * 预加载视频（简化版）
     */
    async preloadVideo(pageKey, videoConfig) {
        console.log(`🔄 Preloading video for ${pageKey}:`, videoConfig.name);
        try {
            await this.loadVideo(pageKey, videoConfig);
        } catch (error) {
            console.log(`⚠️ Preload failed for ${pageKey}:`, error);
        }
    }

    /**
     * 应用备用背景
     */
    applyFallbackBackground(theme) {
        console.log(`🎨 Applying fallback background: ${theme}`);
        
        const fallbackConfig = window.CONFIG?.VIDEOS?.FALLBACK_THEMES?.[theme];
        if (!fallbackConfig) {
            console.error(`❌ Fallback theme not found: ${theme}`);
            return;
        }

        const videoContainer = document.querySelector('.video-background');
        if (videoContainer) {
            videoContainer.style.background = fallbackConfig.gradient;
            videoContainer.style.animation = fallbackConfig.animation;
            videoContainer.classList.add('fallback-active');
        }
    }

    /**
     * 暂停所有视频
     */
    pauseAllVideos() {
        if (this.currentVideo && !this.currentVideo.paused) {
            this.currentVideo.pause();
            console.log('⏸️ Paused current video');
        }
    }

    /**
     * 恢复当前视频
     */
    resumeCurrentVideo() {
        if (this.currentVideo && this.currentVideo.paused) {
            this.currentVideo.play().catch(e => {
                console.log('⚠️ Failed to resume video:', e);
            });
            console.log('▶️ Resumed current video');
        }
    }

    /**
     * 获取缓存状态
     */
    getCacheStatus() {
        return {
            currentVideo: this.currentVideo ? 'loaded' : 'none',
            videoSrc: this.currentVideo ? this.currentVideo.src : 'none'
        };
    }

    /**
     * 开始智能预加载
     */
    async startPreloading() {
        if (this.preloadManager) {
            console.log('🎯 启动智能视频预加载...');
            await this.preloadManager.startPreloading();
        } else {
            console.warn('⚠️ 预加载管理器未初始化');
        }
    }

    /**
     * 获取预加载状态
     */
    getPreloadStatus() {
        if (this.preloadManager) {
            return this.preloadManager.getStatus();
        }
        return { available: false };
    }

    /**
     * 清理预加载缓存
     */
    cleanupPreloadCache() {
        if (this.preloadManager) {
            this.preloadManager.cleanupPreloadedVideos();
        }
    }

    /**
     * 获取当前页面键（兼容方法）
     */
    getCurrentPageKey() {
        const path = window.location.pathname;
        if (path === '/' || path === '/index.html') return 'INDEX';
        if (path === '/meetings' || path === '/meetings.html') return 'MEETINGS';
        if (path === '/anniversary' || path === '/anniversary.html') return 'ANNIVERSARY';
        if (path === '/memorial' || path === '/memorial.html') return 'MEMORIAL';
        if (path === '/together-days' || path === '/together-days.html') return 'TOGETHER_DAYS';
        return 'INDEX';
    }
}

// 全局初始化函数
async function initializeGlobalVideoManager() {
    try {
        console.log('🚀 开始初始化全局VideoManager...');

        // 创建全局实例
        window.VideoManager = new SimpleVideoManager();

        // 等待初始化完成
        await window.VideoManager.initializationPromise;

        console.log('✅ 全局VideoManager初始化完成');

        // 触发自定义事件，通知其他组件VideoManager已就绪
        window.dispatchEvent(new CustomEvent('videoManagerReady', {
            detail: { videoManager: window.VideoManager }
        }));

    } catch (error) {
        console.error('❌ 全局VideoManager初始化失败:', error);

        // 创建一个基础的fallback实例
        window.VideoManager = {
            loadVideo: async () => {
                throw new Error('VideoManager初始化失败，无法加载视频');
            },
            isInitialized: false
        };
    }
}

// 智能初始化：根据文档状态决定初始化时机
if (document.readyState === 'loading') {
    // 文档还在加载，等待DOMContentLoaded
    document.addEventListener('DOMContentLoaded', initializeGlobalVideoManager);
} else {
    // 文档已加载完成，立即初始化
    initializeGlobalVideoManager();
}

// 导出类供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SimpleVideoManager;
}

console.log('📦 Simple Video Manager script loaded');
