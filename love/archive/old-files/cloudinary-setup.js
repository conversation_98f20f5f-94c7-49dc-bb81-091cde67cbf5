// Cloudinary配置和视频管理器
// 替代原有的simple-video-manager.js

class CloudinaryVideoManager {
    constructor(cloudName) {
        this.cloudName = cloudName;
        this.baseUrl = `https://res.cloudinary.com/${cloudName}/video/upload`;
        
        // 视频配置映射 - 2K画质保持策略
        this.videoConfigs = {
            'home': {
                publicId: 'love-website/home',
                transformations: 'q_auto:best,f_auto,w_2560,h_1440,c_limit' // 2K最佳质量
            },
            'meetings': {
                publicId: 'love-website/meetings',
                transformations: 'q_auto:best,f_auto,w_2560,h_1440,c_limit'
            },
            'anniversary': {
                publicId: 'love-website/anniversary',
                transformations: 'q_auto:best,f_auto,w_2560,h_1440,c_limit'
            },
            'together-days': {
                publicId: 'love-website/together-days',
                transformations: 'q_auto:best,f_auto,w_2560,h_1440,c_limit'
            },
            'memorial': {
                publicId: 'love-website/memorial',
                transformations: 'q_auto:best,f_auto,w_2560,h_1440,c_limit'
            }
        };
    }

    // 生成优化的视频URL
    generateVideoUrl(pageName, quality = 'auto') {
        const config = this.videoConfigs[pageName];
        if (!config) {
            console.error(`未找到页面 ${pageName} 的视频配置`);
            return null;
        }

        // 根据设备和网络情况调整质量
        const deviceTransforms = this.getDeviceOptimizations();
        const finalTransforms = `${config.transformations},${deviceTransforms}`;
        
        return `${this.baseUrl}/${finalTransforms}/${config.publicId}.mp4`;
    }

    // 设备优化策略 - 2K画质保持
    getDeviceOptimizations() {
        const isMobile = window.innerWidth <= 768;
        const isTablet = window.innerWidth > 768 && window.innerWidth <= 1024;
        const isSlowConnection = navigator.connection &&
            (navigator.connection.effectiveType === 'slow-2g' ||
             navigator.connection.effectiveType === '2g');

        // 慢网络强制降级
        if (isSlowConnection) {
            return 'q_auto:eco,w_1280,h_720'; // 慢网络使用720p
        }

        // 移动端使用1080p保持清晰度
        if (isMobile) {
            return 'q_auto:good,w_1920,h_1080';
        }

        // 平板使用1440p
        if (isTablet) {
            return 'q_auto:best,w_2048,h_1152';
        }

        // 桌面端使用2K分辨率，最佳质量
        return 'q_auto:best,w_2560,h_1440';
    }

    // 智能加载视频背景
    async loadVideoBackground(pageName, videoElement) {
        try {
            const videoUrl = this.generateVideoUrl(pageName);
            if (!videoUrl) return false;

            // 预加载检测
            const canPlayPromise = new Promise((resolve, reject) => {
                videoElement.addEventListener('canplaythrough', resolve, { once: true });
                videoElement.addEventListener('error', reject, { once: true });
                setTimeout(reject, 10000); // 10秒超时
            });

            videoElement.src = videoUrl;
            videoElement.load();

            await canPlayPromise;
            
            // 成功加载后的处理
            videoElement.style.opacity = '1';
            videoElement.play().catch(e => {
                console.log('自动播放被阻止，等待用户交互');
            });

            console.log(`✅ Cloudinary视频加载成功: ${pageName}`);
            return true;

        } catch (error) {
            console.error(`❌ Cloudinary视频加载失败: ${pageName}`, error);
            this.fallbackToGradient(videoElement);
            return false;
        }
    }

    // 失败时回退到渐变背景
    fallbackToGradient(videoElement) {
        const container = videoElement.parentElement;
        if (container) {
            container.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            videoElement.style.display = 'none';
        }
    }

    // 预加载下一个可能访问的视频
    preloadVideo(pageName) {
        const videoUrl = this.generateVideoUrl(pageName);
        if (videoUrl) {
            const link = document.createElement('link');
            link.rel = 'prefetch';
            link.href = videoUrl;
            document.head.appendChild(link);
        }
    }
}

// 全局初始化
window.CloudinaryVideoManager = CloudinaryVideoManager;

// 使用示例
// const videoManager = new CloudinaryVideoManager('your-cloud-name');
// videoManager.loadVideoBackground('home', document.getElementById('background-video'));
