<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频分辨率调试工具 - Love Website</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #fff;
        }
        
        .debug-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        
        .debug-section h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .info-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .info-card h4 {
            margin-top: 0;
            color: #FFD700;
        }
        
        .code-block {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 10px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-good { background-color: #4CAF50; }
        .status-warning { background-color: #FF9800; }
        .status-error { background-color: #F44336; }
        
        .test-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .video-preview {
            width: 100%;
            max-width: 400px;
            height: 225px;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        .url-display {
            word-break: break-all;
            background: rgba(0, 0, 0, 0.2);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 视频分辨率调试工具</h1>
        <p style="text-align: center; opacity: 0.8;">诊断Love Website视频背景分辨率问题</p>
        
        <div class="debug-section">
            <h3>📱 设备检测信息</h3>
            <div class="info-grid">
                <div class="info-card">
                    <h4>浏览器窗口信息</h4>
                    <div id="window-info">加载中...</div>
                </div>
                <div class="info-card">
                    <h4>设备类型检测</h4>
                    <div id="device-detection">加载中...</div>
                </div>
                <div class="info-card">
                    <h4>网络连接信息</h4>
                    <div id="network-info">加载中...</div>
                </div>
                <div class="info-card">
                    <h4>用户代理信息</h4>
                    <div id="user-agent-info">加载中...</div>
                </div>
            </div>
        </div>
        
        <div class="debug-section">
            <h3>☁️ Cloudinary配置检测</h3>
            <div id="cloudinary-config">加载中...</div>
        </div>
        
        <div class="debug-section">
            <h3>🎬 CDN管理器状态</h3>
            <div id="cdn-manager-status">加载中...</div>
        </div>
        
        <div class="debug-section">
            <h3>🔧 视频URL生成测试</h3>
            <div>
                <button class="test-button" onclick="testVideoUrls()">生成所有页面视频URL</button>
                <button class="test-button" onclick="forceDesktopMode()">强制桌面模式</button>
                <button class="test-button" onclick="forceMobileMode()">强制移动模式</button>
            </div>
            <div id="video-url-results">点击按钮开始测试...</div>
        </div>
        
        <div class="debug-section">
            <h3>🎥 视频预览测试</h3>
            <div>
                <select id="page-selector">
                    <option value="INDEX">首页 (INDEX)</option>
                    <option value="MEETINGS">相遇回忆 (MEETINGS)</option>
                    <option value="ANNIVERSARY">纪念日 (ANNIVERSARY)</option>
                    <option value="MEMORIAL">纪念相册 (MEMORIAL)</option>
                    <option value="TOGETHER_DAYS">在一起的日子 (TOGETHER_DAYS)</option>
                </select>
                <button class="test-button" onclick="loadVideoPreview()">加载视频预览</button>
            </div>
            <div id="video-preview-container">
                <video id="preview-video" class="video-preview" controls muted></video>
                <div id="video-url-display" class="url-display">视频URL将在这里显示</div>
            </div>
        </div>
        
        <div class="debug-section">
            <h3>📊 诊断结果</h3>
            <div id="diagnosis-results">运行测试后将显示诊断结果...</div>
        </div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="/config.js"></script>
    <script src="/cloudinary-setup.js"></script>
    <script src="/hybrid-cdn-manager.js"></script>
    <script src="/cloudinary-load-balancer.js"></script>
    <script src="/simple-video-manager.js"></script>
    
    <script>
        let videoManager = null;
        let forcedDeviceType = null;
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🔍 视频分辨率调试工具启动');
            
            // 显示基本信息
            displayWindowInfo();
            displayDeviceDetection();
            displayNetworkInfo();
            displayUserAgentInfo();
            displayCloudinaryConfig();
            
            // 初始化视频管理器
            await initializeVideoManager();
            displayCDNManagerStatus();
            
            // 运行自动诊断
            runAutoDiagnosis();
        });
        
        function displayWindowInfo() {
            const info = `
                <div><strong>窗口宽度:</strong> ${window.innerWidth}px</div>
                <div><strong>窗口高度:</strong> ${window.innerHeight}px</div>
                <div><strong>屏幕宽度:</strong> ${window.screen.width}px</div>
                <div><strong>屏幕高度:</strong> ${window.screen.height}px</div>
                <div><strong>设备像素比:</strong> ${window.devicePixelRatio}</div>
                <div><strong>可用宽度:</strong> ${window.screen.availWidth}px</div>
                <div><strong>可用高度:</strong> ${window.screen.availHeight}px</div>
            `;
            document.getElementById('window-info').innerHTML = info;
        }
        
        function displayDeviceDetection() {
            const width = window.innerWidth;
            const isMobile = width <= 768;
            const isTablet = width > 768 && width <= 1024;
            const isDesktop = width > 1024;
            
            let deviceType = 'Unknown';
            let statusClass = 'status-warning';
            
            if (forcedDeviceType) {
                deviceType = `${forcedDeviceType} (强制)`;
                statusClass = 'status-warning';
            } else if (isMobile) {
                deviceType = 'Mobile (移动设备)';
                statusClass = width <= 480 ? 'status-good' : 'status-error';
            } else if (isTablet) {
                deviceType = 'Tablet (平板设备)';
                statusClass = 'status-warning';
            } else if (isDesktop) {
                deviceType = 'Desktop (桌面设备)';
                statusClass = 'status-good';
            }
            
            const info = `
                <div><span class="status-indicator ${statusClass}"></span><strong>检测结果:</strong> ${deviceType}</div>
                <div><strong>判断依据:</strong> window.innerWidth = ${width}px</div>
                <div><strong>移动设备阈值:</strong> ≤ 768px</div>
                <div><strong>平板设备范围:</strong> 769px - 1024px</div>
                <div><strong>桌面设备阈值:</strong> > 1024px</div>
                ${width <= 768 && width > 480 ? '<div style="color: #FF9800;"><strong>⚠️ 警告:</strong> 当前窗口可能被误判为移动设备！</div>' : ''}
            `;
            document.getElementById('device-detection').innerHTML = info;
        }
        
        function displayNetworkInfo() {
            const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
            let info = '<div><strong>网络API支持:</strong> ';
            
            if (connection) {
                info += '✅ 支持</div>';
                info += `<div><strong>连接类型:</strong> ${connection.effectiveType || 'Unknown'}</div>`;
                info += `<div><strong>下行速度:</strong> ${connection.downlink || 'Unknown'} Mbps</div>`;
                info += `<div><strong>RTT:</strong> ${connection.rtt || 'Unknown'} ms</div>`;
                
                const isSlowConnection = connection.effectiveType === 'slow-2g' || 
                                       connection.effectiveType === '2g' ||
                                       (connection.effectiveType === '2g' && connection.downlink < 0.5);
                info += `<div><strong>慢速连接:</strong> ${isSlowConnection ? '是' : '否'}</div>`;
            } else {
                info += '❌ 不支持</div>';
            }
            
            document.getElementById('network-info').innerHTML = info;
        }
        
        function displayUserAgentInfo() {
            const ua = navigator.userAgent;
            const isMobileUA = /Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(ua);
            
            const info = `
                <div><strong>User Agent检测:</strong> ${isMobileUA ? 'Mobile' : 'Desktop'}</div>
                <div><strong>完整UA:</strong></div>
                <div class="code-block">${ua}</div>
            `;
            document.getElementById('user-agent-info').innerHTML = info;
        }
        
        function displayCloudinaryConfig() {
            let info = '<div><strong>CONFIG对象:</strong> ';
            
            if (window.CONFIG) {
                info += '✅ 已加载</div>';
                
                if (window.CONFIG.CLOUDINARY) {
                    info += '<div><strong>Cloudinary配置:</strong> ✅ 存在</div>';
                    info += `<div><strong>启用状态:</strong> ${window.CONFIG.CLOUDINARY.ENABLED ? '✅ 启用' : '❌ 禁用'}</div>`;
                    info += `<div><strong>云名称:</strong> ${window.CONFIG.CLOUDINARY.CLOUD_NAME || 'Unknown'}</div>`;
                    
                    if (window.CONFIG.CLOUDINARY.TRANSFORMATIONS) {
                        info += '<div><strong>变换参数:</strong></div>';
                        info += '<div class="code-block">';
                        Object.entries(window.CONFIG.CLOUDINARY.TRANSFORMATIONS).forEach(([key, value]) => {
                            info += `${key}: ${value}<br>`;
                        });
                        info += '</div>';
                    }
                } else {
                    info += '<div><strong>Cloudinary配置:</strong> ❌ 缺失</div>';
                }
            } else {
                info += '❌ 未加载</div>';
            }
            
            document.getElementById('cloudinary-config').innerHTML = info;
        }
        
        async function initializeVideoManager() {
            try {
                if (window.SimpleVideoManager) {
                    videoManager = new window.SimpleVideoManager();
                    await videoManager.initializationPromise;
                    console.log('✅ VideoManager初始化成功');
                } else {
                    console.error('❌ SimpleVideoManager未找到');
                }
            } catch (error) {
                console.error('❌ VideoManager初始化失败:', error);
            }
        }
        
        function displayCDNManagerStatus() {
            let info = '<div><strong>VideoManager状态:</strong> ';
            
            if (videoManager) {
                info += '✅ 已初始化</div>';
                
                if (videoManager.cdnManager) {
                    const managerType = videoManager.cdnManager.constructor.name;
                    info += `<div><strong>CDN管理器类型:</strong> ${managerType}</div>`;
                    info += `<div><strong>Cloudinary启用:</strong> ${videoManager.cloudinaryEnabled ? '✅ 是' : '❌ 否'}</div>`;
                } else {
                    info += '<div><strong>CDN管理器:</strong> ❌ 未初始化 (使用本地文件)</div>';
                }
            } else {
                info += '❌ 未初始化</div>';
            }
            
            document.getElementById('cdn-manager-status').innerHTML = info;
        }
        
        async function testVideoUrls() {
            const pages = ['INDEX', 'MEETINGS', 'ANNIVERSARY', 'MEMORIAL', 'TOGETHER_DAYS'];
            let results = '<h4>视频URL生成结果:</h4>';
            
            for (const page of pages) {
                results += `<div class="info-card">`;
                results += `<h4>${page}</h4>`;
                
                try {
                    if (videoManager && videoManager.cdnManager) {
                        let url = null;
                        
                        // 获取用户上下文
                        const userContext = getUserContextForTest();
                        
                        if (videoManager.cdnManager.generateVideoUrl) {
                            // CloudinaryLoadBalancer
                            url = videoManager.cdnManager.generateVideoUrl(page);
                        } else if (videoManager.cdnManager.generateUrl) {
                            // HybridCDNManager
                            url = videoManager.cdnManager.generateUrl(page, null, userContext);
                        }
                        
                        if (url) {
                            results += `<div class="url-display">${url}</div>`;
                            
                            // 分析URL中的参数
                            const params = extractCloudinaryParams(url);
                            results += `<div><strong>检测到的分辨率:</strong> ${params.resolution || '未指定'}</div>`;
                            results += `<div><strong>质量设置:</strong> ${params.quality || '未指定'}</div>`;
                        } else {
                            results += '<div style="color: #FF9800;">⚠️ 无法生成URL</div>';
                        }
                    } else {
                        results += '<div style="color: #FF9800;">⚠️ CDN管理器未初始化</div>';
                    }
                } catch (error) {
                    results += `<div style="color: #F44336;">❌ 错误: ${error.message}</div>`;
                }
                
                results += '</div>';
            }
            
            document.getElementById('video-url-results').innerHTML = results;
        }
        
        function getUserContextForTest() {
            if (forcedDeviceType) {
                return {
                    isMobile: forcedDeviceType === 'Mobile',
                    isTablet: forcedDeviceType === 'Tablet',
                    isSlowConnection: false,
                    userAgent: navigator.userAgent,
                    screenWidth: window.innerWidth,
                    screenHeight: window.innerHeight,
                    pixelRatio: window.devicePixelRatio
                };
            }
            
            return videoManager ? videoManager.getUserContext() : {
                isMobile: window.innerWidth <= 768,
                isTablet: window.innerWidth > 768 && window.innerWidth <= 1024,
                isSlowConnection: false,
                userAgent: navigator.userAgent,
                screenWidth: window.innerWidth,
                screenHeight: window.innerHeight,
                pixelRatio: window.devicePixelRatio
            };
        }
        
        function extractCloudinaryParams(url) {
            const params = {};
            
            // 提取分辨率参数
            const resolutionMatch = url.match(/w_(\d+),h_(\d+)/);
            if (resolutionMatch) {
                params.resolution = `${resolutionMatch[1]}x${resolutionMatch[2]}`;
            }
            
            // 提取质量参数
            const qualityMatch = url.match(/q_(\w+:?\w*)/);
            if (qualityMatch) {
                params.quality = qualityMatch[1];
            }
            
            return params;
        }
        
        function forceDesktopMode() {
            forcedDeviceType = 'Desktop';
            displayDeviceDetection();
            console.log('🖥️ 强制桌面模式已启用');
        }
        
        function forceMobileMode() {
            forcedDeviceType = 'Mobile';
            displayDeviceDetection();
            console.log('📱 强制移动模式已启用');
        }
        
        async function loadVideoPreview() {
            const pageKey = document.getElementById('page-selector').value;
            const video = document.getElementById('preview-video');
            const urlDisplay = document.getElementById('video-url-display');
            
            try {
                if (videoManager && videoManager.cdnManager) {
                    const userContext = getUserContextForTest();
                    let url = null;
                    
                    if (videoManager.cdnManager.generateVideoUrl) {
                        url = videoManager.cdnManager.generateVideoUrl(pageKey);
                    } else if (videoManager.cdnManager.generateUrl) {
                        url = videoManager.cdnManager.generateUrl(pageKey, null, userContext);
                    }
                    
                    if (url) {
                        video.src = url;
                        urlDisplay.innerHTML = `<strong>视频URL:</strong><br>${url}`;
                        
                        const params = extractCloudinaryParams(url);
                        urlDisplay.innerHTML += `<br><br><strong>分辨率:</strong> ${params.resolution || '未指定'}`;
                        urlDisplay.innerHTML += `<br><strong>质量:</strong> ${params.quality || '未指定'}`;
                    } else {
                        urlDisplay.innerHTML = '❌ 无法生成视频URL';
                    }
                } else {
                    urlDisplay.innerHTML = '❌ CDN管理器未初始化';
                }
            } catch (error) {
                urlDisplay.innerHTML = `❌ 错误: ${error.message}`;
            }
        }
        
        function runAutoDiagnosis() {
            let diagnosis = '<h4>🔍 自动诊断结果:</h4>';
            
            // 检查窗口大小问题
            const width = window.innerWidth;
            if (width <= 768 && width > 480) {
                diagnosis += '<div style="color: #FF9800;"><span class="status-indicator status-warning"></span><strong>可能问题:</strong> 浏览器窗口宽度 (' + width + 'px) 可能导致设备被误判为移动设备</div>';
                diagnosis += '<div><strong>建议:</strong> 尝试将浏览器窗口调整到更大尺寸，或使用"强制桌面模式"按钮</div>';
            }
            
            // 检查配置问题
            if (window.CONFIG && window.CONFIG.CLOUDINARY && window.CONFIG.CLOUDINARY.TRANSFORMATIONS) {
                const transforms = window.CONFIG.CLOUDINARY.TRANSFORMATIONS;
                if (transforms.DESKTOP_2K && !transforms.DESKTOP_2K.includes('w_')) {
                    diagnosis += '<div style="color: #FF9800;"><span class="status-indicator status-warning"></span><strong>配置问题:</strong> DESKTOP_2K参数缺少明确的分辨率设置</div>';
                }
            }
            
            // 检查CDN管理器
            if (!videoManager || !videoManager.cdnManager) {
                diagnosis += '<div style="color: #F44336;"><span class="status-indicator status-error"></span><strong>严重问题:</strong> CDN管理器未正确初始化</div>';
            }
            
            if (diagnosis === '<h4>🔍 自动诊断结果:</h4>') {
                diagnosis += '<div style="color: #4CAF50;"><span class="status-indicator status-good"></span>未发现明显问题，请运行视频URL测试进行进一步诊断</div>';
            }
            
            document.getElementById('diagnosis-results').innerHTML = diagnosis;
        }
    </script>
</body>
</html>
