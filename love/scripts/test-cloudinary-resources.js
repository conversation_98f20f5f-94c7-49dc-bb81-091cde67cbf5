#!/usr/bin/env node

/**
 * 测试Cloudinary资源脚本
 * 检查实际上传的视频资源和正确的URL格式
 */

const cloudinary = require('cloudinary').v2;

// 多账户配置
const ACCOUNTS = {
    'dcglebc2w': {
        cloudName: 'dcglebc2w',
        apiKey: process.env.CLOUDINARY_API_KEY_DCGLEBC2W || '***************',
        apiSecret: process.env.CLOUDINARY_API_SECRET_DCGLEBC2W || 'FfwmlQJX_0LOszwF6YF9KbnhmoU'
    }
};

// 测试不同的URL格式
const TEST_URLS = [
    // 原始格式
    'https://res.cloudinary.com/dcglebc2w/video/upload/q_100,f_auto,w_2560,h_1440,c_limit,fl_immutable_cache/love-website/home.mp4',
    
    // 简化格式
    'https://res.cloudinary.com/dcglebc2w/video/upload/love-website/home.mp4',
    
    // 不同的public ID格式
    'https://res.cloudinary.com/dcglebc2w/video/upload/home.mp4',
    'https://res.cloudinary.com/dcglebc2w/video/upload/love-website-home.mp4',
    'https://res.cloudinary.com/dcglebc2w/video/upload/love_website_home.mp4',
    
    // 带基本变换的格式
    'https://res.cloudinary.com/dcglebc2w/video/upload/q_auto,f_auto/love-website/home.mp4',
    'https://res.cloudinary.com/dcglebc2w/video/upload/q_auto/love-website/home.mp4',
    
    // 不带变换参数
    'https://res.cloudinary.com/dcglebc2w/video/upload/love-website/home',
];

// 测试URL是否可访问
async function testUrl(url) {
    try {
        const response = await fetch(url, { method: 'HEAD' });
        return {
            url,
            status: response.status,
            success: response.ok,
            headers: {
                contentType: response.headers.get('content-type'),
                contentLength: response.headers.get('content-length')
            }
        };
    } catch (error) {
        return {
            url,
            status: 'ERROR',
            success: false,
            error: error.message
        };
    }
}

// 列出Cloudinary账户中的资源
async function listCloudinaryResources(accountId) {
    const account = ACCOUNTS[accountId];
    if (!account) {
        console.error(`❌ 账户不存在: ${accountId}`);
        return;
    }
    
    // 配置Cloudinary
    cloudinary.config({
        cloud_name: account.cloudName,
        api_key: account.apiKey,
        api_secret: account.apiSecret
    });
    
    try {
        console.log(`\n🔍 检查账户: ${account.cloudName}`);
        
        // 列出视频资源
        const result = await cloudinary.api.resources({
            resource_type: 'video',
            max_results: 50
        });
        
        console.log(`📊 找到 ${result.resources.length} 个视频资源:`);
        
        result.resources.forEach((resource, index) => {
            console.log(`${index + 1}. Public ID: ${resource.public_id}`);
            console.log(`   URL: ${resource.secure_url}`);
            console.log(`   尺寸: ${resource.width}x${resource.height}`);
            console.log(`   大小: ${(resource.bytes / 1024 / 1024).toFixed(2)}MB`);
            console.log(`   格式: ${resource.format}`);
            console.log('---');
        });
        
        return result.resources;
        
    } catch (error) {
        console.error(`❌ 获取资源失败: ${error.message}`);
        return [];
    }
}

// 生成正确的URL
function generateCorrectUrl(publicId, transforms = '') {
    const baseUrl = 'https://res.cloudinary.com/dcglebc2w/video/upload';
    
    if (transforms) {
        return `${baseUrl}/${transforms}/${publicId}.mp4`;
    } else {
        return `${baseUrl}/${publicId}.mp4`;
    }
}

// 主测试函数
async function runTests() {
    console.log('🧪 开始Cloudinary资源测试...\n');
    
    // 1. 列出实际的资源
    console.log('=== 第一步: 检查实际上传的资源 ===');
    const resources = await listCloudinaryResources('dcglebc2w');
    
    // 2. 测试不同的URL格式
    console.log('\n=== 第二步: 测试URL格式 ===');
    
    for (const url of TEST_URLS) {
        console.log(`\n🔗 测试URL: ${url}`);
        const result = await testUrl(url);
        
        if (result.success) {
            console.log(`✅ 状态: ${result.status} - 可访问`);
            console.log(`📄 类型: ${result.headers.contentType}`);
            console.log(`📊 大小: ${result.headers.contentLength} bytes`);
        } else {
            console.log(`❌ 状态: ${result.status} - 不可访问`);
            if (result.error) {
                console.log(`🔍 错误: ${result.error}`);
            }
        }
    }
    
    // 3. 基于实际资源生成正确URL
    if (resources && resources.length > 0) {
        console.log('\n=== 第三步: 基于实际资源生成URL ===');
        
        const homeResource = resources.find(r => 
            r.public_id.includes('home') || 
            r.public_id.includes('index') ||
            r.public_id.includes('flower')
        );
        
        if (homeResource) {
            console.log(`\n🎯 找到首页视频资源: ${homeResource.public_id}`);
            
            // 生成不同变换的URL
            const transforms = [
                '',
                'q_auto',
                'q_auto,f_auto',
                'q_100,f_auto,w_2560,h_1440',
                'q_100,f_auto,w_2560,h_1440,c_limit',
                'q_100,f_auto,w_2560,h_1440,c_limit,fl_immutable_cache'
            ];
            
            for (const transform of transforms) {
                const url = generateCorrectUrl(homeResource.public_id, transform);
                console.log(`\n🔗 测试变换: ${transform || '无变换'}`);
                console.log(`URL: ${url}`);
                
                const result = await testUrl(url);
                if (result.success) {
                    console.log(`✅ 可访问 (${result.status})`);
                } else {
                    console.log(`❌ 不可访问 (${result.status})`);
                }
            }
        } else {
            console.log('⚠️ 未找到首页相关的视频资源');
        }
    }
    
    console.log('\n🎉 测试完成！');
}

// 执行测试
if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = { testUrl, listCloudinaryResources, generateCorrectUrl };
