#!/bin/bash

# 批量为所有页面添加紧急修复脚本
# 使用方法: chmod +x scripts/update-all-pages-emergency-fix.sh && ./scripts/update-all-pages-emergency-fix.sh

echo "🚨 开始为所有页面添加紧急修复脚本..."

# 定义需要更新的HTML文件
HTML_FILES=(
    "html/anniversary.html"
    "html/memorial.html" 
    "html/together-days.html"
)

# 检查紧急修复脚本是否存在
if [ ! -f "scripts/emergency-video-fix.js" ]; then
    echo "❌ 紧急修复脚本不存在: scripts/emergency-video-fix.js"
    exit 1
fi

echo "✅ 紧急修复脚本存在"

# 为每个HTML文件添加紧急修复脚本引用
for file in "${HTML_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "🔧 处理文件: $file"
        
        # 检查是否已经包含紧急修复脚本
        if grep -q "emergency-video-fix.js" "$file"; then
            echo "   ✅ 已包含紧急修复脚本，跳过"
            continue
        fi
        
        # 查找simple-video-manager.js的位置并在其后添加紧急修复脚本
        if grep -q "simple-video-manager.js" "$file"; then
            # 使用sed在simple-video-manager.js后添加紧急修复脚本
            sed -i 's|<script src="/simple-video-manager.js"></script>|<script src="/simple-video-manager.js"></script>\n    <script src="/scripts/emergency-video-fix.js"></script>|g' "$file"
            echo "   ✅ 已添加紧急修复脚本"
        else
            echo "   ⚠️ 未找到simple-video-manager.js引用，手动添加到</body>前"
            # 在</body>前添加脚本
            sed -i 's|</body>|    <script src="/scripts/emergency-video-fix.js"></script>\n</body>|g' "$file"
            echo "   ✅ 已在</body>前添加紧急修复脚本"
        fi
    else
        echo "❌ 文件不存在: $file"
    fi
done

echo ""
echo "🎯 验证更新结果:"

# 验证所有文件是否都包含了紧急修复脚本
for file in "${HTML_FILES[@]}"; do
    if [ -f "$file" ]; then
        if grep -q "emergency-video-fix.js" "$file"; then
            echo "✅ $file - 包含紧急修复脚本"
        else
            echo "❌ $file - 缺少紧急修复脚本"
        fi
    fi
done

# 同时检查index.html和meetings.html
echo ""
echo "🔍 检查其他页面:"

if grep -q "emergency-video-fix.js" "html/index.html"; then
    echo "✅ html/index.html - 包含紧急修复脚本"
else
    echo "❌ html/index.html - 缺少紧急修复脚本"
fi

if grep -q "emergency-video-fix.js" "html/meetings.html"; then
    echo "✅ html/meetings.html - 包含紧急修复脚本"
else
    echo "❌ html/meetings.html - 缺少紧急修复脚本"
fi

echo ""
echo "✅ 批量更新完成！"
echo ""
echo "📋 接下来的步骤:"
echo "1. 访问 https://love.yuh.cool/test/test-emergency-fix.html 测试修复效果"
echo "2. 访问各个页面验证视频是否正常加载"
echo "3. 检查浏览器控制台确认没有错误"
echo ""
echo "🧪 测试页面:"
echo "- 紧急修复测试: https://love.yuh.cool/test/test-emergency-fix.html"
echo "- 分辨率调试: https://love.yuh.cool/test/test-video-resolution-debug.html"
echo "- 修复效果测试: https://love.yuh.cool/test/test-video-fix.html"
