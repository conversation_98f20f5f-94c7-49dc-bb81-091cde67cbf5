/**
 * Love Website Configuration
 * 统一配置文件 - 管理所有路径、API端点和网站设置
 * 
 * 使用方法：
 * 1. 在HTML中引入：<script src="/config.js"></script>
 * 2. 在JavaScript中使用：CONFIG.API.BASE_URL, CONFIG.PATHS.FONTS 等
 */

// 全局配置对象
window.CONFIG = {
    // Cloudinary 配置 - 多账户管理模式
    CLOUDINARY: {
        // 向后兼容的单账户配置
        CLOUD_NAME: 'dcglebc2w', // 主账户云名称
        ENABLED: true, // 设置为true启用Cloudinary
        BASE_URL: 'https://res.cloudinary.com',
        TRANSFORMATIONS: {
            // 高画质策略 - 保持压缩后的最佳质量
            DEFAULT: 'q_auto:best,f_auto,fl_immutable_cache', // 自动最佳质量，强制缓存
            DESKTOP_2K: 'q_auto:best,f_auto,fl_immutable_cache', // 桌面端最佳质量
            DESKTOP_4K: 'q_auto:best,f_auto,fl_immutable_cache', // 4K支持
            MOBILE_HD: 'q_auto:good,f_auto,fl_immutable_cache',  // 移动端高质量
            FALLBACK: 'q_auto:eco,f_auto'    // 仅在网络极差时使用
        },

        // 多账户配置 - 每个页面使用专用账户
        MULTI_ACCOUNT: {
            ENABLED: true, // 启用多账户模式

            // 账户配置 - 注意：API密钥应通过环境变量管理，此处仅用于前端账户识别
            ACCOUNTS: {
                'dcglebc2w': {
                    cloudName: 'dcglebc2w',
                    baseUrl: 'https://res.cloudinary.com/dcglebc2w',
                    quota: 25, // GB/月
                    assignedPages: ['INDEX'],
                    priority: 1,
                    status: 'active',
                    description: '主账户 - 首页专用'
                },
                'drhqbbqxz': {
                    cloudName: 'drhqbbqxz',
                    baseUrl: 'https://res.cloudinary.com/drhqbbqxz',
                    quota: 25,
                    assignedPages: ['ANNIVERSARY'],
                    priority: 1,
                    status: 'active',
                    description: '纪念日页面专用'
                },
                'dkqnm9nwr': {
                    cloudName: 'dkqnm9nwr',
                    baseUrl: 'https://res.cloudinary.com/dkqnm9nwr',
                    quota: 25,
                    assignedPages: ['MEETINGS'],
                    priority: 1,
                    status: 'active',
                    description: '相遇回忆页面专用'
                },
                'ds14sv2gh': {
                    cloudName: 'ds14sv2gh',
                    baseUrl: 'https://res.cloudinary.com/ds14sv2gh',
                    quota: 25,
                    assignedPages: ['MEMORIAL'],
                    priority: 1,
                    status: 'active',
                    description: '纪念相册页面专用'
                },
                'dpq95x5nf': {
                    cloudName: 'dpq95x5nf',
                    baseUrl: 'https://res.cloudinary.com/dpq95x5nf',
                    quota: 25,
                    assignedPages: ['TOGETHER_DAYS'],
                    priority: 1,
                    status: 'active',
                    description: '在一起的日子页面专用'
                },
                'dtsgvqrna': {
                    cloudName: 'dtsgvqrna',
                    baseUrl: 'https://res.cloudinary.com/dtsgvqrna',
                    quota: 25,
                    assignedPages: [], // 备用账户，不分配特定页面
                    priority: 2,
                    status: 'standby',
                    description: '备用账户 - 故障转移使用'
                }
            },

            // 页面到账户的映射关系
            PAGE_ACCOUNT_MAPPING: {
                'INDEX': 'dcglebc2w',
                'ANNIVERSARY': 'drhqbbqxz',
                'MEETINGS': 'dkqnm9nwr',
                'MEMORIAL': 'ds14sv2gh',
                'TOGETHER_DAYS': 'dpq95x5nf'
            },

            // 故障转移策略
            FAILOVER: {
                ENABLED: true,
                BACKUP_ACCOUNTS: ['dtsgvqrna'], // 备用账户列表
                MAX_RETRIES: 3,
                RETRY_DELAY: 2000, // 毫秒
                HEALTH_CHECK_INTERVAL: 300000 // 5分钟检查一次
            },

            // 负载均衡策略
            LOAD_BALANCING: {
                STRATEGY: 'page-dedicated', // 'page-dedicated', 'round-robin', 'weighted'
                ENABLE_CROSS_ACCOUNT_FALLBACK: true, // 允许跨账户故障转移
                QUOTA_THRESHOLD: 0.9 // 配额使用超过90%时触发故障转移
            }
        }
    },

    // API 配置
    API: {
        BASE_URL: '/api',
        ENDPOINTS: {
            MESSAGES: '/messages',
            HEALTH: '/health',
            TOGETHER_DAYS: '/together-days'
        }
    },

    // 静态资源路径配置
    PATHS: {
        // 字体文件路径
        FONTS: '/fonts',
        
        // 背景资源路径
        BACKGROUND: '/background',
        
        // JavaScript 文件路径
        SCRIPTS: {
            ROMANTIC_QUOTES: '/romantic-quotes.js',
            MODERN_QUOTES_DATA: '/modern-quotes-data.js',
            MAIN_SCRIPT: '/script.js'
        },
        
        // CSS 文件路径
        STYLES: {
            MAIN: '/style.css'
        }
    },

    // 字体配置
    FONTS: {
        COURGETTE: {
            name: 'Courgette',
            file: 'Courgette-Regular.ttf',
            get url() { return `${window.CONFIG.PATHS.FONTS}/${this.file}`; }
        },
        GREAT_VIBES: {
            name: 'Great Vibes',
            file: 'GreatVibes-Regular.ttf',
            get url() { return `${window.CONFIG.PATHS.FONTS}/${this.file}`; }
        },
        ZI_XIAO_HUN_GOU_YU: {
            name: 'ZiXiaoHunGouYu',
            file: '字小魂勾玉行书(商用需授权).ttf',
            get url() { return `${window.CONFIG.PATHS.FONTS}/${this.file}`; }
        },
        ZI_XIAO_HUN_SAN_FEN: {
            name: 'ZiXiaoHunSanFen',
            file: '字小魂三分行楷(商用需授权).ttf',
            get url() { return `${window.CONFIG.PATHS.FONTS}/${this.file}`; }
        },
        ZI_HUN_XING_YUN_FEI_BAI: {
            name: 'ZiHunXingYunFeiBai',
            file: '字魂行云飞白体(商用需授权).ttf',
            get url() { return `${window.CONFIG.PATHS.FONTS}/${this.file}`; }
        }
    },

    // 视频背景配置
    VIDEOS: {
        // 页面视频配置映射
        PAGES: {
            INDEX: {
                name: '花朵背景',
                file: 'home/home.mp4',
                theme: 'flower',
                priority: 1, // 最高优先级
                get url() { return `${window.CONFIG.PATHS.BACKGROUND}/${this.file}`; }
            },
            MEETINGS: {
                name: '星河背景',
                file: 'meetings/meetings.mp4',
                theme: 'stars',
                priority: 2,
                get url() { return `${window.CONFIG.PATHS.BACKGROUND}/${this.file}`; }
            },
            ANNIVERSARY: {
                name: '绿荫背景',
                file: 'anniversary/anniversary.mp4',
                theme: 'green',
                priority: 3,
                get url() { return `${window.CONFIG.PATHS.BACKGROUND}/${this.file}`; }
            },
            MEMORIAL: {
                name: '海洋背景',
                file: 'memorial/memorial.mp4',
                theme: 'sea',
                priority: 4,
                get url() { return `${window.CONFIG.PATHS.BACKGROUND}/${this.file}`; }
            },
            TOGETHER_DAYS: {
                name: '海底背景',
                file: 'together-days/together-days.mp4',
                theme: 'lake',
                priority: 5,
                get url() { return `${window.CONFIG.PATHS.BACKGROUND}/${this.file}`; }
            }
        },

        // 页面到视频的映射关系
        PAGE_MAPPING: {
            'index.html': 'INDEX',
            'meetings.html': 'MEETINGS',
            'anniversary.html': 'ANNIVERSARY',
            'memorial.html': 'MEMORIAL',
            'together-days.html': 'TOGETHER_DAYS'
        },

        // 备用背景主题映射
        FALLBACK_THEMES: {
            'flower': {
                gradient: 'linear-gradient(135deg, #fce7f3 0%, #fbcfe8 15%, #f9a8d4 30%, #f472b6 45%, #ec4899 60%, #db2777 75%, #be185d 90%, #9d174d 100%)',
                animation: 'gradientShift 15s ease infinite'
            },
            'stars': {
                gradient: 'linear-gradient(135deg, #0c0c2e 0%, #1a1a3e 25%, #2d1b69 50%, #4a148c 75%, #6a1b9a 100%)',
                animation: 'starTwinkle 20s ease infinite'
            },
            'green': {
                gradient: 'linear-gradient(135deg, #22c55e 0%, #16a34a 25%, #15803d 50%, #166534 75%, #14532d 100%)',
                animation: 'leafSway 18s ease infinite'
            },
            'sea': {
                gradient: 'linear-gradient(135deg, #0ea5e9 0%, #0284c7 25%, #0369a1 50%, #075985 75%, #0c4a6e 100%)',
                animation: 'waveFlow 16s ease infinite'
            },
            'lake': {
                gradient: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 25%, #0e7490 50%, #155e75 75%, #164e63 100%)',
                animation: 'rippleEffect 14s ease infinite'
            }
        },

        // 网络策略配置（增强版）
        NETWORK_STRATEGIES: {
            FAST: {
                preload: 'auto',
                maxConcurrent: 3,
                enablePreload: true,
                timeout: 15000,
                quality: '1080p',
                progressive: true
            },
            MEDIUM: {
                preload: 'metadata',
                maxConcurrent: 2,
                enablePreload: true,
                timeout: 20000,
                quality: '720p',
                progressive: true
            },
            SLOW: {
                preload: 'none',
                maxConcurrent: 1,
                enablePreload: false,
                timeout: 45000,
                quality: '480p',
                progressive: false
            },
            UNKNOWN: {
                preload: 'metadata',
                maxConcurrent: 2,
                enablePreload: true,
                timeout: 25000,
                quality: '720p',
                progressive: true
            }
        },

        // 视频质量配置
        QUALITY_SETTINGS: {
            '1080p': {
                width: 1920,
                height: 1080,
                bitrate: '2500k',
                suffix: '_1080p'
            },
            '720p': {
                width: 1280,
                height: 720,
                bitrate: '1500k',
                suffix: '_720p'
            },
            '480p': {
                width: 854,
                height: 480,
                bitrate: '800k',
                suffix: '_480p'
            }
        },

        // 压缩格式支持
        COMPRESSION_FORMATS: {
            WEBM: {
                extension: '.webm',
                codec: 'vp9',
                priority: 1
            },
            H264: {
                extension: '.mp4',
                codec: 'h264',
                priority: 2
            }
        },

        // 缓存策略参数
        CACHE_CONFIG: {
            maxCacheSize: 4,           // 最大缓存视频数量
            cleanupInterval: 60000,    // 清理间隔(毫秒)
            maxAge: 300000,           // 最大缓存时间(5分钟)
            memoryThreshold: 0.8      // 内存使用阈值
        },

        // 预加载优先级队列
        PRELOAD_QUEUE: [
            { page: 'INDEX', delay: 0 },        // 立即加载
            { page: 'MEETINGS', delay: 2000 },  // 2秒后加载
            { page: 'ANNIVERSARY', delay: 5000 }, // 5秒后加载
            { page: 'MEMORIAL', delay: 8000 },   // 8秒后加载
            { page: 'TOGETHER_DAYS', delay: 10000 } // 10秒后加载
        ],

        // 工具函数
        UTILS: {
            // 根据页面获取视频配置
            getVideoConfig: function(pageName) {
                const pageKey = window.CONFIG.VIDEOS.PAGE_MAPPING[pageName];
                return pageKey ? window.CONFIG.VIDEOS.PAGES[pageKey] : null;
            },

            // 根据当前页面获取视频配置
            getCurrentVideoConfig: function() {
                const path = window.location.pathname;
                const filename = path.split('/').pop() || 'index.html';
                return this.getVideoConfig(filename);
            },

            // 获取备用背景配置
            getFallbackConfig: function(theme) {
                return window.CONFIG.VIDEOS.FALLBACK_THEMES[theme] || window.CONFIG.VIDEOS.FALLBACK_THEMES['flower'];
            },

            // 根据网络类型获取策略
            getNetworkStrategy: function(networkType) {
                return window.CONFIG.VIDEOS.NETWORK_STRATEGIES[networkType] || window.CONFIG.VIDEOS.NETWORK_STRATEGIES['UNKNOWN'];
            },

            // 获取预加载队列
            getPreloadQueue: function() {
                return window.CONFIG.VIDEOS.PRELOAD_QUEUE.slice(); // 返回副本
            }
        },

        // 向后兼容 - 保留原有的FLOWER_BACKGROUND配置
        FLOWER_BACKGROUND: {
            name: '花朵背景',
            file: '花/123-enhanced-[problembo.com].mp4',
            get url() { return `${window.CONFIG.PATHS.BACKGROUND}/${encodeURIComponent(this.file)}`; },
            get encodedUrl() { return `${window.CONFIG.PATHS.BACKGROUND}/%E8%8A%B1/123-enhanced-%5Bproblembo.com%5D.mp4`; }
        }
    },

    // 网站基本信息
    SITE: {
        NAME: 'Love Website',
        TITLE: '我们的爱情故事',
        DESCRIPTION: '记录我们美好的爱情时光',
        VERSION: '2.0.0'
    },

    // 页面路由配置
    ROUTES: {
        HOME: '/',
        TOGETHER_DAYS: '/together-days',
        ANNIVERSARY: '/anniversary',
        MEETINGS: '/meetings',
        MEMORIAL: '/memorial'
    },

    // 域名和服务器配置
    DOMAIN: {
        // 生产环境域名
        PRODUCTION: 'love.yuh.cool',

        // 开发环境域名
        DEVELOPMENT: 'localhost:1314',

        // 获取当前环境的域名
        get current() {
            return window.CONFIG.ENV.isDevelopment ? this.DEVELOPMENT : this.PRODUCTION;
        },

        // 获取完整的基础URL
        get baseUrl() {
            const protocol = window.CONFIG.ENV.isDevelopment ? 'http' : 'https';
            return `${protocol}://${this.current}`;
        },

        // 获取API的完整URL
        get apiUrl() {
            return `${this.baseUrl}${window.CONFIG.API.BASE_URL}`;
        }
    },

    // 服务器配置
    SERVER: {
        // 内部端口
        PORT: 1314,

        // 外部端口
        EXTERNAL_PORT: {
            HTTP: 80,
            HTTPS: 443
        },

        // 数据库配置
        DATABASE: {
            PATH: './data/love_messages.db',
            BACKUP_DIR: './data/backups'
        },

        // 日志配置
        LOGS: {
            DIR: './logs',
            FILE: 'backend.log'
        }
    },

    // 开发/生产环境配置
    ENV: {
        // 自动检测环境
        get isDevelopment() {
            return window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
        },
        get isProduction() {
            return !this.isDevelopment;
        },

        // 获取当前环境名称
        get name() {
            return this.isDevelopment ? 'development' : 'production';
        }
    },

    // 智能预加载配置
    PRELOAD: {
        // 是否启用预加载
        ENABLED: true,

        // 预加载优先级（数字越小优先级越高）
        PRIORITY: {
            'ANNIVERSARY': 1,    // 纪念日页面优先级最高
            'MEETINGS': 2,       // 相遇页面
            'MEMORIAL': 3,       // 纪念物页面
            'TOGETHER_DAYS': 4   // 在一起的日子页面
        },

        // 网络状况阈值（KB/s）
        NETWORK_THRESHOLDS: {
            FAST: 10000,    // 10MB/s
            MEDIUM: 2000,   // 2MB/s
            SLOW: 500       // 500KB/s
        },

        // 预加载时机配置（毫秒）
        TIMING: {
            AFTER_MAIN_VIDEO: 3000,  // 主页视频加载完成后3秒开始预加载
            BETWEEN_PRELOADS: 1000,  // 预加载间隔1秒
            NETWORK_CHECK: 5000,     // 网络状况检查间隔5秒
            CLEANUP_INTERVAL: 300000 // 缓存清理间隔5分钟
        },

        // 并发控制
        CONCURRENCY: {
            MAX_CONCURRENT: 2,       // 默认最大并发预加载数
            FAST_NETWORK: 3,         // 快速网络时的并发数
            SLOW_NETWORK: 1          // 慢速网络时的并发数
        },

        // 视频大小估算（KB）
        SIZE_ESTIMATES: {
            'flower': 15000,  // 15MB
            'stars': 20000,   // 20MB
            'green': 12000,   // 12MB
            'sea': 18000,     // 18MB
            'lake': 16000     // 16MB
        },

        // 缓存管理
        CACHE: {
            MAX_AGE: 600000,         // 预加载缓存最大保存时间10分钟
            CLEANUP_THRESHOLD: 0.8   // 缓存清理阈值80%
        }
    },

    // 工具函数
    UTILS: {
        // 获取完整的API URL（相对路径）
        getApiUrl: function(endpoint = '') {
            return `${window.CONFIG.API.BASE_URL}${endpoint}`;
        },

        // 获取完整的API URL（绝对路径）
        getFullApiUrl: function(endpoint = '') {
            return `${window.CONFIG.DOMAIN.apiUrl}${endpoint}`;
        },

        // 获取完整的资源URL
        getAssetUrl: function(path) {
            return path.startsWith('/') ? path : `/${path}`;
        },

        // 获取完整的页面URL
        getPageUrl: function(path = '') {
            const cleanPath = path.startsWith('/') ? path : `/${path}`;
            return `${window.CONFIG.DOMAIN.baseUrl}${cleanPath}`;
        },
        
        // 获取字体CSS规则
        getFontFaceCSS: function(fontConfig) {
            return `
                @font-face {
                    font-family: '${fontConfig.name}';
                    src: url('${fontConfig.url}') format('truetype');
                    font-weight: normal;
                    font-style: normal;
                    font-display: swap;
                }
            `;
        },
        
        // 动态加载CSS
        loadCSS: function(css) {
            const style = document.createElement('style');
            style.textContent = css;
            document.head.appendChild(style);
        },
        
        // 动态加载所有字体
        loadAllFonts: function() {
            const fonts = window.CONFIG.FONTS;
            let allFontCSS = '';
            
            Object.values(fonts).forEach(font => {
                allFontCSS += this.getFontFaceCSS(font);
            });
            
            this.loadCSS(allFontCSS);
        }
    }
};

// 自动初始化（可选）
if (typeof window !== 'undefined') {
    // 页面加载完成后自动加载字体（如果需要）
    document.addEventListener('DOMContentLoaded', function() {
        // 可以在这里添加自动初始化逻辑
        console.log('Love Website Config Loaded:', window.CONFIG.SITE.VERSION);
    });
}

// 兼容性：同时导出为模块（如果支持）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = window.CONFIG;
}
