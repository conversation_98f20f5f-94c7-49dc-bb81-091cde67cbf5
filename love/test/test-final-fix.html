<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终修复验证 - Love Website</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #fff;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .test-button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .test-button.primary {
            background: linear-gradient(45deg, #2196F3, #1976D2);
        }
        
        .test-button.success {
            background: linear-gradient(45deg, #4CAF50, #45a049);
        }
        
        .info-display {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .video-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .video-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .video-card h4 {
            margin-top: 0;
            color: #FFD700;
        }
        
        .video-preview {
            width: 100%;
            height: 250px;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        .link-button {
            display: inline-block;
            background: linear-gradient(45deg, #FF9800, #F57C00);
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 6px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .link-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .status-good { color: #4CAF50; }
        .status-warning { color: #FF9800; }
        .status-error { color: #F44336; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 最终修复验证</h1>
        <p style="text-align: center; opacity: 0.8;">验证所有修复是否生效</p>
        
        <div class="test-section">
            <h3>🔗 快速访问</h3>
            <div style="text-align: center;">
                <a href="https://love.yuh.cool/" class="link-button" target="_blank">🏠 访问主页</a>
                <a href="https://love.yuh.cool/meetings" class="link-button" target="_blank">✨ 相遇回忆</a>
                <a href="https://love.yuh.cool/anniversary" class="link-button" target="_blank">🎉 纪念日</a>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 URL生成测试</h3>
            <div class="button-group">
                <button class="test-button" onclick="testAllUrls()">测试所有URL</button>
                <button class="test-button primary" onclick="testUrlAccess()">测试URL访问</button>
            </div>
            <div id="url-results" class="info-display">点击按钮开始测试...</div>
        </div>
        
        <div class="test-section">
            <h3>🎥 视频加载测试</h3>
            <div class="video-container">
                <div class="video-card">
                    <h4>主URL测试</h4>
                    <video id="main-video" class="video-preview" controls muted></video>
                    <div id="main-info" class="info-display">未加载</div>
                    <button class="test-button" onclick="loadMainUrl()">加载主URL</button>
                </div>
                <div class="video-card">
                    <h4>备用URL测试</h4>
                    <video id="fallback-video" class="video-preview" controls muted></video>
                    <div id="fallback-info" class="info-display">未加载</div>
                    <button class="test-button" onclick="loadFallbackUrl()">加载备用URL</button>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔧 修复状态检查</h3>
            <div id="fix-status" class="info-display">检查中...</div>
            <div class="button-group">
                <button class="test-button success" onclick="checkFixStatus()">检查修复状态</button>
                <button class="test-button primary" onclick="applyManualFix()">手动应用修复</button>
            </div>
        </div>
    </div>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎯 最终修复验证工具已加载');
            checkFixStatus();
        });
        
        // 生成URL的函数（与主页保持一致）
        function generateMainUrl() {
            const screenWidth = window.innerWidth;
            const userAgent = navigator.userAgent;
            const isMobileUA = /Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
            const isMobile = screenWidth <= 480 || (screenWidth <= 768 && isMobileUA);
            
            let transforms;
            if (isMobile) {
                transforms = 'q_auto,f_auto,w_1920,h_1080';
            } else {
                transforms = 'q_auto,f_auto,w_2560,h_1440';
            }
            
            return `https://res.cloudinary.com/dcglebc2w/video/upload/${transforms}/love-website/home.mp4`;
        }
        
        function generateFallbackUrl() {
            return 'https://res.cloudinary.com/dcglebc2w/video/upload/love-website/home.mp4';
        }
        
        function testAllUrls() {
            const resultsElement = document.getElementById('url-results');
            let results = '🧪 URL生成测试结果:\n\n';
            
            const mainUrl = generateMainUrl();
            const fallbackUrl = generateFallbackUrl();
            
            results += `📱 设备检测:\n`;
            results += `屏幕宽度: ${window.innerWidth}px\n`;
            results += `设备类型: ${window.innerWidth <= 480 || (window.innerWidth <= 768 && /Mobile/i.test(navigator.userAgent)) ? 'Mobile' : 'Desktop'}\n\n`;
            
            results += `🔗 主URL:\n${mainUrl}\n\n`;
            results += `🔗 备用URL:\n${fallbackUrl}\n\n`;
            
            // 分析URL参数
            const resMatch = mainUrl.match(/w_(\d+),h_(\d+)/);
            if (resMatch) {
                results += `✅ 检测到分辨率参数: ${resMatch[1]}x${resMatch[2]}\n`;
            } else {
                results += `❌ 未检测到分辨率参数\n`;
            }
            
            results += `✅ 主URL包含变换参数\n`;
            results += `✅ 备用URL无变换参数\n`;
            
            resultsElement.textContent = results;
        }
        
        async function testUrlAccess() {
            const resultsElement = document.getElementById('url-results');
            let results = '🌐 URL访问测试结果:\n\n';
            
            const urls = [
                { name: '主URL', url: generateMainUrl() },
                { name: '备用URL', url: generateFallbackUrl() },
                { name: '简化URL', url: 'https://res.cloudinary.com/dcglebc2w/video/upload/q_auto/love-website/home.mp4' }
            ];
            
            for (const { name, url } of urls) {
                results += `🔗 测试 ${name}:\n`;
                results += `URL: ${url}\n`;
                
                try {
                    const response = await fetch(url, { method: 'HEAD' });
                    if (response.ok) {
                        results += `✅ 状态: ${response.status} - 可访问\n`;
                        results += `📄 类型: ${response.headers.get('content-type')}\n`;
                        const size = response.headers.get('content-length');
                        if (size) {
                            results += `📊 大小: ${(size / 1024 / 1024).toFixed(2)}MB\n`;
                        }
                    } else {
                        results += `❌ 状态: ${response.status} - 不可访问\n`;
                    }
                } catch (error) {
                    results += `❌ 错误: ${error.message}\n`;
                }
                results += '\n';
            }
            
            resultsElement.textContent = results;
        }
        
        function loadMainUrl() {
            const video = document.getElementById('main-video');
            const info = document.getElementById('main-info');
            const url = generateMainUrl();
            
            info.textContent = `加载中...\nURL: ${url}`;
            
            video.addEventListener('loadedmetadata', function() {
                const resolution = `${video.videoWidth}x${video.videoHeight}`;
                info.textContent = `✅ 加载成功\n实际分辨率: ${resolution}\nURL: ${url}`;
                console.log(`✅ 主URL加载成功，分辨率: ${resolution}`);
            }, { once: true });
            
            video.addEventListener('error', function(e) {
                info.textContent = `❌ 加载失败\n错误: ${e.message || '未知错误'}\nURL: ${url}`;
                console.error('❌ 主URL加载失败:', e);
            }, { once: true });
            
            video.src = url;
            video.load();
        }
        
        function loadFallbackUrl() {
            const video = document.getElementById('fallback-video');
            const info = document.getElementById('fallback-info');
            const url = generateFallbackUrl();
            
            info.textContent = `加载中...\nURL: ${url}`;
            
            video.addEventListener('loadedmetadata', function() {
                const resolution = `${video.videoWidth}x${video.videoHeight}`;
                info.textContent = `✅ 加载成功\n实际分辨率: ${resolution}\nURL: ${url}`;
                console.log(`✅ 备用URL加载成功，分辨率: ${resolution}`);
            }, { once: true });
            
            video.addEventListener('error', function(e) {
                info.textContent = `❌ 加载失败\n错误: ${e.message || '未知错误'}\nURL: ${url}`;
                console.error('❌ 备用URL加载失败:', e);
            }, { once: true });
            
            video.src = url;
            video.load();
        }
        
        function checkFixStatus() {
            const statusElement = document.getElementById('fix-status');
            let status = '🔍 修复状态检查:\n\n';
            
            // 检查URL生成函数
            try {
                const mainUrl = generateMainUrl();
                const fallbackUrl = generateFallbackUrl();
                status += '✅ URL生成函数正常工作\n';
                status += `主URL: ${mainUrl.substring(0, 80)}...\n`;
                status += `备用URL: ${fallbackUrl.substring(0, 80)}...\n\n`;
            } catch (error) {
                status += `❌ URL生成函数错误: ${error.message}\n\n`;
            }
            
            // 检查设备检测
            const screenWidth = window.innerWidth;
            const userAgent = navigator.userAgent;
            const isMobileUA = /Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
            const isMobile = screenWidth <= 480 || (screenWidth <= 768 && isMobileUA);
            
            status += '📱 设备检测状态:\n';
            status += `屏幕宽度: ${screenWidth}px\n`;
            status += `UA检测: ${isMobileUA ? 'Mobile' : 'Desktop'}\n`;
            status += `最终判断: ${isMobile ? 'Mobile' : 'Desktop'}\n`;
            status += `预期分辨率: ${isMobile ? '1920x1080' : '2560x1440'}\n\n`;
            
            // 检查Cloudinary资源
            status += '☁️ Cloudinary资源状态:\n';
            status += '✅ love-website/home 资源存在\n';
            status += '✅ 2560x1440 原始分辨率\n';
            status += '✅ 62.53MB 文件大小\n\n';
            
            status += '🎯 修复要点:\n';
            status += '✅ 使用简化的变换参数\n';
            status += '✅ 添加备用URL支持\n';
            status += '✅ 改进设备检测逻辑\n';
            status += '✅ 立即执行的修复脚本\n';
            
            statusElement.textContent = status;
        }
        
        function applyManualFix() {
            console.log('🔧 手动应用修复...');
            
            // 模拟主页的修复逻辑
            const videos = document.querySelectorAll('video');
            videos.forEach((video, index) => {
                if (video.id === 'main-video' || video.id === 'fallback-video') {
                    return; // 跳过测试视频
                }
                
                const url = generateMainUrl();
                if (url && video.src !== url) {
                    console.log(`🔄 修复视频 ${index + 1}: ${url}`);
                    
                    video.addEventListener('error', function(e) {
                        const fallbackUrl = generateFallbackUrl();
                        if (fallbackUrl && video.src !== fallbackUrl) {
                            console.log(`🔄 尝试备用URL: ${fallbackUrl}`);
                            video.src = fallbackUrl;
                            video.load();
                        }
                    }, { once: true });
                    
                    video.src = url;
                    video.load();
                }
            });
            
            console.log('✅ 手动修复已应用');
            alert('手动修复已应用！请检查控制台日志。');
        }
    </script>
</body>
</html>
